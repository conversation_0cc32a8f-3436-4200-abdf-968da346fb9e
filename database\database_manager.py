#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات - Database Manager
إدارة جميع عمليات قاعدة البيانات لنظام نقطة البيع
"""

import sqlite3
import os
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import json

class DatabaseManager:
    """مدير قاعدة البيانات الرئيسي"""
    
    def __init__(self, db_path: str = "pos_database.db"):
        self.db_path = db_path
        self.connection = None
        
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        if self.connection is None:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row
        return self.connection
    
    def close_connection(self):
        """إغلاق اتصال قاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def initialize_database(self) -> bool:
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # إنشاء جدول المستخدمين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT NOT NULL DEFAULT 'cashier',
                    email TEXT,
                    phone TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    permissions TEXT DEFAULT '{}'
                )
            """)
            
            # إنشاء جدول الفئات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    parent_id INTEGER,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (parent_id) REFERENCES categories (id)
                )
            """)
            
            # إنشاء جدول المنتجات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    barcode TEXT UNIQUE,
                    name TEXT NOT NULL,
                    description TEXT,
                    category_id INTEGER,
                    purchase_price REAL DEFAULT 0,
                    selling_price REAL NOT NULL,
                    stock_quantity INTEGER DEFAULT 0,
                    min_stock_level INTEGER DEFAULT 5,
                    unit TEXT DEFAULT 'قطعة',
                    image_path TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )
            """)
            
            # إنشاء جدول العملاء
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    loyalty_points INTEGER DEFAULT 0,
                    total_purchases REAL DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إنشاء جدول المبيعات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sales (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_number TEXT UNIQUE NOT NULL,
                    customer_id INTEGER,
                    user_id INTEGER NOT NULL,
                    total_amount REAL NOT NULL,
                    discount_amount REAL DEFAULT 0,
                    tax_amount REAL DEFAULT 0,
                    paid_amount REAL NOT NULL,
                    change_amount REAL DEFAULT 0,
                    payment_method TEXT DEFAULT 'نقدي',
                    status TEXT DEFAULT 'مكتملة',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            # إنشاء جدول تفاصيل المبيعات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sale_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sale_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity INTEGER NOT NULL,
                    unit_price REAL NOT NULL,
                    total_price REAL NOT NULL,
                    discount_amount REAL DEFAULT 0,
                    FOREIGN KEY (sale_id) REFERENCES sales (id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            """)
            
            # إنشاء جدول حركات المخزون
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS inventory_movements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER NOT NULL,
                    movement_type TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    reference_id INTEGER,
                    reference_type TEXT,
                    notes TEXT,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            # إنشاء جدول إعدادات النظام
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key TEXT UNIQUE NOT NULL,
                    setting_value TEXT,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إنشاء جدول سجل العمليات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS audit_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    table_name TEXT,
                    record_id INTEGER,
                    old_values TEXT,
                    new_values TEXT,
                    ip_address TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            # إنشاء الفهارس لتحسين الأداء
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(created_at)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_sales_user ON sales(user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_inventory_product ON inventory_movements(product_id)")
            
            # إدراج البيانات الافتراضية
            self._insert_default_data(cursor)
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False
    
    def _insert_default_data(self, cursor):
        """إدراج البيانات الافتراضية"""
        try:
            # إنشاء مستخدم افتراضي (admin)
            admin_password = self._hash_password("admin123")
            cursor.execute("""
                INSERT OR IGNORE INTO users 
                (username, password_hash, full_name, role, permissions) 
                VALUES (?, ?, ?, ?, ?)
            """, ("admin", admin_password, "المدير العام", "admin", 
                  json.dumps({"all": True})))
            
            # إنشاء كاشير افتراضي
            cashier_password = self._hash_password("cashier123")
            cursor.execute("""
                INSERT OR IGNORE INTO users 
                (username, password_hash, full_name, role, permissions) 
                VALUES (?, ?, ?, ?, ?)
            """, ("cashier", cashier_password, "الكاشير", "cashier", 
                  json.dumps({"sales": True, "products_view": True})))
            
            # إنشاء فئات افتراضية
            default_categories = [
                ("مواد غذائية", "المواد الغذائية والمشروبات"),
                ("إلكترونيات", "الأجهزة الإلكترونية والكهربائية"),
                ("ملابس", "الملابس والأحذية"),
                ("مستحضرات تجميل", "مستحضرات التجميل والعناية"),
                ("أدوات منزلية", "الأدوات والمعدات المنزلية")
            ]
            
            for name, desc in default_categories:
                cursor.execute("""
                    INSERT OR IGNORE INTO categories (name, description) 
                    VALUES (?, ?)
                """, (name, desc))
            
            # إعدادات النظام الافتراضية
            default_settings = [
                ("store_name", "متجر التقنية المتقدمة", "اسم المتجر"),
                ("store_address", "الجزائر العاصمة", "عنوان المتجر"),
                ("store_phone", "+213-XXX-XXXXXX", "هاتف المتجر"),
                ("tax_rate", "19", "معدل الضريبة (%)"),
                ("currency", "دج", "العملة"),
                ("receipt_footer", "شكراً لزيارتكم", "تذييل الفاتورة"),
                ("auto_backup", "1", "النسخ الاحتياطي التلقائي"),
                ("low_stock_alert", "1", "تنبيه نفاد المخزون")
            ]
            
            for key, value, desc in default_settings:
                cursor.execute("""
                    INSERT OR IGNORE INTO system_settings 
                    (setting_key, setting_value, description) 
                    VALUES (?, ?, ?)
                """, (key, value, desc))
                
        except Exception as e:
            print(f"خطأ في إدراج البيانات الافتراضية: {e}")
    
    def _hash_password(self, password: str) -> str:
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_user_login(self, username: str, password: str) -> Optional[Dict]:
        """التحقق من بيانات تسجيل الدخول"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            password_hash = self._hash_password(password)
            
            cursor.execute("""
                SELECT id, username, full_name, role, permissions, is_active
                FROM users 
                WHERE username = ? AND password_hash = ? AND is_active = 1
            """, (username, password_hash))
            
            user = cursor.fetchone()
            
            if user:
                # تحديث وقت آخر تسجيل دخول
                cursor.execute("""
                    UPDATE users SET last_login = CURRENT_TIMESTAMP 
                    WHERE id = ?
                """, (user['id'],))
                conn.commit()
                
                return {
                    'id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'role': user['role'],
                    'permissions': json.loads(user['permissions'] or '{}'),
                    'is_active': user['is_active']
                }
            
            return None
            
        except Exception as e:
            print(f"خطأ في التحقق من تسجيل الدخول: {e}")
            return None

    # ===== إدارة المنتجات =====

    def add_product(self, product_data: Dict) -> bool:
        """إضافة منتج جديد"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO products
                (barcode, name, description, category_id, purchase_price,
                 selling_price, stock_quantity, min_stock_level, unit, image_path)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                product_data.get('barcode'),
                product_data['name'],
                product_data.get('description', ''),
                product_data.get('category_id'),
                product_data.get('purchase_price', 0),
                product_data['selling_price'],
                product_data.get('stock_quantity', 0),
                product_data.get('min_stock_level', 5),
                product_data.get('unit', 'قطعة'),
                product_data.get('image_path')
            ))

            conn.commit()
            return True

        except Exception as e:
            print(f"خطأ في إضافة المنتج: {e}")
            return False

    def get_product_by_barcode(self, barcode: str) -> Optional[Dict]:
        """البحث عن منتج بالباركود"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT p.*, c.name as category_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.barcode = ? AND p.is_active = 1
            """, (barcode,))

            product = cursor.fetchone()
            return dict(product) if product else None

        except Exception as e:
            print(f"خطأ في البحث عن المنتج: {e}")
            return None

    def get_all_products(self, active_only: bool = True) -> List[Dict]:
        """الحصول على جميع المنتجات"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            query = """
                SELECT p.*, c.name as category_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
            """

            if active_only:
                query += " WHERE p.is_active = 1"

            query += " ORDER BY p.name"

            cursor.execute(query)
            products = cursor.fetchall()

            return [dict(product) for product in products]

        except Exception as e:
            print(f"خطأ في جلب المنتجات: {e}")
            return []

    def update_product_stock(self, product_id: int, new_quantity: int,
                           movement_type: str = "تعديل", user_id: int = None) -> bool:
        """تحديث مخزون المنتج"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # الحصول على الكمية الحالية
            cursor.execute("SELECT stock_quantity FROM products WHERE id = ?", (product_id,))
            current_stock = cursor.fetchone()

            if not current_stock:
                return False

            old_quantity = current_stock['stock_quantity']
            quantity_change = new_quantity - old_quantity

            # تحديث المخزون
            cursor.execute("""
                UPDATE products
                SET stock_quantity = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (new_quantity, product_id))

            # تسجيل حركة المخزون
            cursor.execute("""
                INSERT INTO inventory_movements
                (product_id, movement_type, quantity, user_id, notes)
                VALUES (?, ?, ?, ?, ?)
            """, (product_id, movement_type, quantity_change, user_id,
                  f"تحديث من {old_quantity} إلى {new_quantity}"))

            conn.commit()
            return True

        except Exception as e:
            print(f"خطأ في تحديث المخزون: {e}")
            return False

    # ===== إدارة المبيعات =====

    def create_sale(self, sale_data: Dict, sale_items: List[Dict]) -> Optional[str]:
        """إنشاء عملية بيع جديدة"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # إنشاء رقم فاتورة فريد
            invoice_number = self._generate_invoice_number()

            # إدراج بيانات البيع
            cursor.execute("""
                INSERT INTO sales
                (invoice_number, customer_id, user_id, total_amount,
                 discount_amount, tax_amount, paid_amount, change_amount,
                 payment_method, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                invoice_number,
                sale_data.get('customer_id'),
                sale_data['user_id'],
                sale_data['total_amount'],
                sale_data.get('discount_amount', 0),
                sale_data.get('tax_amount', 0),
                sale_data['paid_amount'],
                sale_data.get('change_amount', 0),
                sale_data.get('payment_method', 'نقدي'),
                sale_data.get('notes', '')
            ))

            sale_id = cursor.lastrowid

            # إدراج عناصر البيع وتحديث المخزون
            for item in sale_items:
                cursor.execute("""
                    INSERT INTO sale_items
                    (sale_id, product_id, quantity, unit_price, total_price, discount_amount)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    sale_id,
                    item['product_id'],
                    item['quantity'],
                    item['unit_price'],
                    item['total_price'],
                    item.get('discount_amount', 0)
                ))

                # تحديث المخزون
                cursor.execute("""
                    UPDATE products
                    SET stock_quantity = stock_quantity - ?
                    WHERE id = ?
                """, (item['quantity'], item['product_id']))

                # تسجيل حركة المخزون
                cursor.execute("""
                    INSERT INTO inventory_movements
                    (product_id, movement_type, quantity, reference_id,
                     reference_type, user_id)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (item['product_id'], "بيع", -item['quantity'],
                      sale_id, "sale", sale_data['user_id']))

            conn.commit()
            return invoice_number

        except Exception as e:
            print(f"خطأ في إنشاء البيع: {e}")
            conn.rollback()
            return None

    def _generate_invoice_number(self) -> str:
        """توليد رقم فاتورة فريد"""
        now = datetime.now()
        date_part = now.strftime("%Y%m%d")

        conn = self.get_connection()
        cursor = conn.cursor()

        # البحث عن آخر رقم فاتورة لليوم الحالي
        cursor.execute("""
            SELECT COUNT(*) as count FROM sales
            WHERE DATE(created_at) = DATE('now')
        """)

        count = cursor.fetchone()['count'] + 1

        return f"INV-{date_part}-{count:04d}"
