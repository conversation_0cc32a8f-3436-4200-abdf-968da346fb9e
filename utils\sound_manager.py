#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الأصوات - Sound Manager
إدارة التنبيهات الصوتية والتأثيرات الصوتية
"""

import os
import threading
from typing import Optional
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtMultimedia import QSound, QMediaPlayer, QMediaContent
from PyQt5.QtCore import QUrl

class SoundManager(QObject):
    """مدير الأصوات والتنبيهات"""
    
    # إشارات الأصوات
    sound_played = pyqtSignal(str)
    sound_error = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.enabled = True
        self.volume = 0.7
        self.sounds_path = "sounds/"
        self.media_player = QMediaPlayer()
        self.setup_sounds()
        
    def setup_sounds(self):
        """إعداد الأصوات"""
        # إنشاء مجلد الأصوات إذا لم يكن موجوداً
        if not os.path.exists(self.sounds_path):
            os.makedirs(self.sounds_path)
        
        # تعريف الأصوات المطلوبة
        self.sound_files = {
            'beep': 'beep.wav',
            'success': 'success.wav',
            'error': 'error.wav',
            'warning': 'warning.wav',
            'scan': 'scan.wav',
            'cash_register': 'cash_register.wav',
            'notification': 'notification.wav',
            'button_click': 'click.wav'
        }
        
        # إنشاء أصوات افتراضية إذا لم تكن موجودة
        self.create_default_sounds()
    
    def create_default_sounds(self):
        """إنشاء أصوات افتراضية باستخدام النظام"""
        try:
            # استخدام أصوات النظام الافتراضية
            import winsound
            self.use_system_sounds = True
        except ImportError:
            self.use_system_sounds = False
    
    def set_enabled(self, enabled: bool):
        """تفعيل أو إلغاء تفعيل الأصوات"""
        self.enabled = enabled
    
    def set_volume(self, volume: float):
        """تعيين مستوى الصوت (0.0 - 1.0)"""
        self.volume = max(0.0, min(1.0, volume))
        self.media_player.setVolume(int(self.volume * 100))
    
    def play_sound(self, sound_name: str):
        """تشغيل صوت محدد"""
        if not self.enabled:
            return
        
        try:
            if sound_name in self.sound_files:
                self._play_sound_file(sound_name)
            else:
                self._play_system_sound(sound_name)
                
        except Exception as e:
            self.sound_error.emit(f"خطأ في تشغيل الصوت {sound_name}: {e}")
    
    def _play_sound_file(self, sound_name: str):
        """تشغيل ملف صوتي"""
        sound_file = os.path.join(self.sounds_path, self.sound_files[sound_name])
        
        if os.path.exists(sound_file):
            # استخدام QMediaPlayer للملفات الصوتية
            url = QUrl.fromLocalFile(os.path.abspath(sound_file))
            content = QMediaContent(url)
            self.media_player.setMedia(content)
            self.media_player.play()
            self.sound_played.emit(sound_name)
        else:
            # استخدام أصوات النظام كبديل
            self._play_system_sound(sound_name)
    
    def _play_system_sound(self, sound_name: str):
        """تشغيل أصوات النظام"""
        if self.use_system_sounds:
            import winsound
            
            # ربط الأصوات بأصوات النظام
            system_sounds = {
                'beep': winsound.MB_OK,
                'success': winsound.MB_OK,
                'error': winsound.MB_ICONHAND,
                'warning': winsound.MB_ICONEXCLAMATION,
                'scan': winsound.MB_OK,
                'cash_register': winsound.MB_OK,
                'notification': winsound.MB_ICONASTERISK,
                'button_click': winsound.MB_OK
            }
            
            if sound_name in system_sounds:
                threading.Thread(
                    target=lambda: winsound.MessageBeep(system_sounds[sound_name]),
                    daemon=True
                ).start()
                self.sound_played.emit(sound_name)
    
    # أصوات محددة للعمليات
    
    def play_scan_sound(self):
        """صوت مسح الباركود"""
        self.play_sound('scan')
    
    def play_success_sound(self):
        """صوت النجاح"""
        self.play_sound('success')
    
    def play_error_sound(self):
        """صوت الخطأ"""
        self.play_sound('error')
    
    def play_warning_sound(self):
        """صوت التحذير"""
        self.play_sound('warning')
    
    def play_cash_register_sound(self):
        """صوت ماكينة النقد"""
        self.play_sound('cash_register')
    
    def play_notification_sound(self):
        """صوت الإشعار"""
        self.play_sound('notification')
    
    def play_button_click_sound(self):
        """صوت النقر على الزر"""
        self.play_sound('button_click')
    
    def play_beep(self):
        """صوت بيب بسيط"""
        self.play_sound('beep')

class VoiceAnnouncer(QObject):
    """مُعلن صوتي للعمليات"""
    
    def __init__(self, sound_manager: SoundManager):
        super().__init__()
        self.sound_manager = sound_manager
        self.enabled = True
        self.language = 'ar'
        
        # محاولة استيراد مكتبة النطق
        try:
            import pyttsx3
            self.tts_engine = pyttsx3.init()
            self.tts_available = True
            self.setup_tts()
        except ImportError:
            self.tts_engine = None
            self.tts_available = False
    
    def setup_tts(self):
        """إعداد محرك النطق"""
        if self.tts_available:
            # تعيين الصوت العربي إن أمكن
            voices = self.tts_engine.getProperty('voices')
            for voice in voices:
                if 'arabic' in voice.name.lower() or 'ar' in voice.id.lower():
                    self.tts_engine.setProperty('voice', voice.id)
                    break
            
            # تعيين سرعة النطق
            self.tts_engine.setProperty('rate', 150)
            
            # تعيين مستوى الصوت
            self.tts_engine.setProperty('volume', 0.8)
    
    def set_enabled(self, enabled: bool):
        """تفعيل أو إلغاء تفعيل الإعلان الصوتي"""
        self.enabled = enabled
    
    def announce(self, text: str):
        """إعلان نص صوتياً"""
        if not self.enabled or not self.tts_available:
            return
        
        try:
            # تشغيل النطق في خيط منفصل
            threading.Thread(
                target=self._speak_text,
                args=(text,),
                daemon=True
            ).start()
        except Exception as e:
            print(f"خطأ في الإعلان الصوتي: {e}")
    
    def _speak_text(self, text: str):
        """نطق النص"""
        try:
            self.tts_engine.say(text)
            self.tts_engine.runAndWait()
        except Exception as e:
            print(f"خطأ في نطق النص: {e}")
    
    # إعلانات محددة للعمليات
    
    def announce_product_added(self, product_name: str, price: float):
        """إعلان إضافة منتج"""
        text = f"تم إضافة {product_name} بسعر {price:.2f} دينار"
        self.announce(text)
    
    def announce_total(self, total: float):
        """إعلان المجموع"""
        text = f"المجموع الإجمالي {total:.2f} دينار"
        self.announce(text)
    
    def announce_payment_received(self, amount: float):
        """إعلان استلام الدفع"""
        text = f"تم استلام {amount:.2f} دينار"
        self.announce(text)
    
    def announce_change(self, change: float):
        """إعلان الباقي"""
        if change > 0:
            text = f"الباقي {change:.2f} دينار"
            self.announce(text)
    
    def announce_sale_complete(self):
        """إعلان اكتمال البيع"""
        text = "تم إتمام البيع بنجاح"
        self.announce(text)
    
    def announce_error(self, error_message: str):
        """إعلان خطأ"""
        text = f"خطأ: {error_message}"
        self.announce(text)
    
    def announce_low_stock(self, product_name: str, quantity: int):
        """إعلان نفاد المخزون"""
        text = f"تحذير: مخزون {product_name} منخفض، متبقي {quantity} قطع"
        self.announce(text)

class SoundNotificationManager(QObject):
    """مدير إشعارات الصوت المتقدم"""
    
    def __init__(self):
        super().__init__()
        self.sound_manager = SoundManager()
        self.voice_announcer = VoiceAnnouncer(self.sound_manager)
        self.notification_queue = []
        self.is_playing = False
        
        # مؤقت لمعالجة قائمة الإشعارات
        self.queue_timer = QTimer()
        self.queue_timer.timeout.connect(self.process_notification_queue)
        self.queue_timer.start(100)  # فحص كل 100 مللي ثانية
    
    def set_sound_enabled(self, enabled: bool):
        """تفعيل الأصوات"""
        self.sound_manager.set_enabled(enabled)
    
    def set_voice_enabled(self, enabled: bool):
        """تفعيل الإعلان الصوتي"""
        self.voice_announcer.set_enabled(enabled)
    
    def set_volume(self, volume: float):
        """تعيين مستوى الصوت"""
        self.sound_manager.set_volume(volume)
    
    def add_notification(self, sound_type: str, message: str = "", priority: int = 1):
        """إضافة إشعار للقائمة"""
        notification = {
            'sound_type': sound_type,
            'message': message,
            'priority': priority,
            'timestamp': threading.time.time()
        }
        
        # إدراج حسب الأولوية
        inserted = False
        for i, existing in enumerate(self.notification_queue):
            if priority > existing['priority']:
                self.notification_queue.insert(i, notification)
                inserted = True
                break
        
        if not inserted:
            self.notification_queue.append(notification)
    
    def process_notification_queue(self):
        """معالجة قائمة الإشعارات"""
        if not self.is_playing and self.notification_queue:
            notification = self.notification_queue.pop(0)
            self.play_notification(notification)
    
    def play_notification(self, notification: dict):
        """تشغيل إشعار"""
        self.is_playing = True
        
        # تشغيل الصوت
        self.sound_manager.play_sound(notification['sound_type'])
        
        # الإعلان الصوتي إذا كان هناك رسالة
        if notification['message']:
            self.voice_announcer.announce(notification['message'])
        
        # إعادة تعيين حالة التشغيل بعد تأخير
        QTimer.singleShot(1000, lambda: setattr(self, 'is_playing', False))
    
    # طرق سريعة للإشعارات الشائعة
    
    def notify_scan_success(self, product_name: str, price: float):
        """إشعار نجاح المسح"""
        self.sound_manager.play_scan_sound()
        self.voice_announcer.announce_product_added(product_name, price)
    
    def notify_scan_error(self):
        """إشعار خطأ المسح"""
        self.sound_manager.play_error_sound()
        self.voice_announcer.announce("منتج غير موجود")
    
    def notify_sale_complete(self, total: float):
        """إشعار اكتمال البيع"""
        self.sound_manager.play_cash_register_sound()
        self.voice_announcer.announce_sale_complete()
    
    def notify_low_stock(self, product_name: str, quantity: int):
        """إشعار نفاد المخزون"""
        self.sound_manager.play_warning_sound()
        self.voice_announcer.announce_low_stock(product_name, quantity)
    
    def notify_payment_received(self, amount: float, change: float = 0):
        """إشعار استلام الدفع"""
        self.sound_manager.play_success_sound()
        self.voice_announcer.announce_payment_received(amount)
        if change > 0:
            self.voice_announcer.announce_change(change)
