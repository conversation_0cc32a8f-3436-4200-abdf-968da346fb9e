/* ===== Animation System ===== */

/* ===== Base Animations ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes scaleOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: var(--neu-shadow-outset);
    }
    50% {
        box-shadow: var(--neu-shadow-outset), 0 0 20px rgba(52, 152, 219, 0.4);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes wiggle {
    0%, 7% {
        transform: rotateZ(0);
    }
    15% {
        transform: rotateZ(-15deg);
    }
    20% {
        transform: rotateZ(10deg);
    }
    25% {
        transform: rotateZ(-10deg);
    }
    30% {
        transform: rotateZ(6deg);
    }
    35% {
        transform: rotateZ(-4deg);
    }
    40%, 100% {
        transform: rotateZ(0);
    }
}

@keyframes heartbeat {
    0% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.3);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.3);
    }
    70% {
        transform: scale(1);
    }
}

/* ===== Animation Classes ===== */
.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.animate-fade-out {
    animation: fadeOut 0.5s ease forwards;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease forwards;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease forwards;
}

.animate-slide-in-up {
    animation: slideInUp 0.6s ease forwards;
}

.animate-slide-in-down {
    animation: slideInDown 0.6s ease forwards;
}

.animate-scale-in {
    animation: scaleIn 0.4s ease forwards;
}

.animate-scale-out {
    animation: scaleOut 0.4s ease forwards;
}

.animate-bounce {
    animation: bounce 1s ease;
}

.animate-shake {
    animation: shake 0.5s ease;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-wiggle {
    animation: wiggle 1s ease;
}

.animate-heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
}

/* ===== Hover Animations ===== */
.hover-lift {
    transition: var(--neu-transition);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--neu-shadow-hover);
}

.hover-scale {
    transition: var(--neu-transition);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: var(--neu-transition);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: var(--neu-transition);
}

.hover-glow:hover {
    box-shadow: var(--neu-shadow-outset), 0 0 20px rgba(52, 152, 219, 0.3);
}

.hover-shimmer {
    position: relative;
    overflow: hidden;
}

.hover-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.hover-shimmer:hover::before {
    left: 100%;
}

/* ===== Loading Animations ===== */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(52, 152, 219, 0.2);
    border-top: 4px solid var(--neu-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--neu-primary);
    animation: loadingDots 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes loadingDots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.loading-wave {
    display: inline-flex;
    gap: 2px;
}

.loading-wave span {
    width: 4px;
    height: 20px;
    background: var(--neu-primary);
    border-radius: 2px;
    animation: loadingWave 1.2s ease-in-out infinite;
}

.loading-wave span:nth-child(1) {
    animation-delay: -1.2s;
}

.loading-wave span:nth-child(2) {
    animation-delay: -1.1s;
}

.loading-wave span:nth-child(3) {
    animation-delay: -1s;
}

.loading-wave span:nth-child(4) {
    animation-delay: -0.9s;
}

.loading-wave span:nth-child(5) {
    animation-delay: -0.8s;
}

@keyframes loadingWave {
    0%, 40%, 100% {
        transform: scaleY(0.4);
    }
    20% {
        transform: scaleY(1);
    }
}

/* ===== Progress Animations ===== */
.progress-bar-animated {
    position: relative;
    overflow: hidden;
}

.progress-bar-animated::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 20px 20px;
    animation: progressStripes 1s linear infinite;
}

@keyframes progressStripes {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 20px 0;
    }
}

/* ===== Notification Animations ===== */
.notification-enter {
    animation: slideInRight 0.3s ease forwards;
}

.notification-exit {
    animation: slideInRight 0.3s ease reverse forwards;
}

/* ===== Modal Animations ===== */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -50px);
}

.modal.show .modal-dialog {
    transform: none;
}

/* ===== Tab Animations ===== */
.tab-content {
    animation: fadeIn 0.3s ease;
}

.tab-switching {
    animation: fadeOut 0.15s ease;
}

/* ===== Button Press Animation ===== */
.btn-press {
    transition: var(--neu-transition);
}

.btn-press:active {
    transform: scale(0.95);
    box-shadow: var(--neu-shadow-pressed);
}

/* ===== Card Flip Animation ===== */
.card-flip {
    perspective: 1000px;
}

.card-flip-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.card-flip:hover .card-flip-inner {
    transform: rotateY(180deg);
}

.card-flip-front,
.card-flip-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: var(--neu-radius);
}

.card-flip-back {
    transform: rotateY(180deg);
}

/* ===== Stagger Animation ===== */
.stagger-animation > * {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeIn 0.5s ease forwards;
}

.stagger-animation > *:nth-child(1) {
    animation-delay: 0.1s;
}

.stagger-animation > *:nth-child(2) {
    animation-delay: 0.2s;
}

.stagger-animation > *:nth-child(3) {
    animation-delay: 0.3s;
}

.stagger-animation > *:nth-child(4) {
    animation-delay: 0.4s;
}

.stagger-animation > *:nth-child(5) {
    animation-delay: 0.5s;
}

/* ===== Reduced Motion Support ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .animate-pulse,
    .animate-spin,
    .animate-bounce,
    .animate-float,
    .animate-heartbeat {
        animation: none;
    }
    
    .loading-spinner,
    .loading-dots span,
    .loading-wave span {
        animation: none;
    }
}

/* ===== Performance Optimizations ===== */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000;
}

/* ===== Custom Easing Functions ===== */
.ease-out-back {
    transition-timing-function: cubic-bezier(0.34, 1.56, 0.64, 1);
}

.ease-in-back {
    transition-timing-function: cubic-bezier(0.6, -0.28, 0.735, 0.045);
}

.ease-in-out-back {
    transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.ease-elastic {
    transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
