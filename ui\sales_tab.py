#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تبويب المبيعات - Sales Tab
واجهة إدارة المبيعات ونقطة البيع
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget, 
                            QTableWidgetItem, QFrame, QComboBox, QSpinBox,
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QDoubleSpinBox, QTextEdit, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QKeySequence
from PyQt5.QtWidgets import QShortcut

class SalesTab(QWidget):
    """تبويب المبيعات الرئيسي"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.current_sale_items = []
        self.current_total = 0.0
        self.setup_ui()
        self.setup_connections()
        self.setup_shortcuts()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # الجانب الأيسر - إدخال المنتجات والعمليات
        left_panel = self.create_left_panel()
        main_layout.addWidget(left_panel, 1)
        
        # الجانب الأيمن - عرض الفاتورة والمجاميع
        right_panel = self.create_right_panel()
        main_layout.addWidget(right_panel, 2)
    
    def create_left_panel(self):
        """إنشاء اللوحة اليسرى"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 15px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # عنوان اللوحة
        title = QLabel("🛒 إدخال المنتجات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # مجموعة البحث والإدخال
        search_group = QGroupBox("البحث والإدخال")
        search_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        search_layout = QVBoxLayout(search_group)
        
        # حقل الباركود
        barcode_label = QLabel("الباركود:")
        barcode_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
        
        self.barcode_input = QLineEdit()
        self.barcode_input.setPlaceholderText("امسح الباركود أو أدخله يدوياً")
        self.barcode_input.setStyleSheet("""
            QLineEdit {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                color: #2c3e50;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
            }
        """)
        
        # زر البحث
        search_button = QPushButton("🔍 بحث")
        search_button.clicked.connect(self.search_product)
        
        search_layout.addWidget(barcode_label)
        search_layout.addWidget(self.barcode_input)
        search_layout.addWidget(search_button)
        
        # معلومات المنتج المحدد
        product_group = QGroupBox("معلومات المنتج")
        product_group.setStyleSheet(search_group.styleSheet())
        product_layout = QGridLayout(product_group)
        
        # اسم المنتج
        product_layout.addWidget(QLabel("اسم المنتج:"), 0, 0)
        self.product_name_label = QLabel("لم يتم اختيار منتج")
        self.product_name_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        product_layout.addWidget(self.product_name_label, 0, 1)
        
        # السعر
        product_layout.addWidget(QLabel("السعر:"), 1, 0)
        self.product_price_label = QLabel("0.00 دج")
        self.product_price_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        product_layout.addWidget(self.product_price_label, 1, 1)
        
        # المخزون المتاح
        product_layout.addWidget(QLabel("المخزون:"), 2, 0)
        self.product_stock_label = QLabel("0")
        self.product_stock_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        product_layout.addWidget(self.product_stock_label, 2, 1)
        
        # الكمية المطلوبة
        product_layout.addWidget(QLabel("الكمية:"), 3, 0)
        self.quantity_spinbox = QSpinBox()
        self.quantity_spinbox.setMinimum(1)
        self.quantity_spinbox.setMaximum(9999)
        self.quantity_spinbox.setValue(1)
        self.quantity_spinbox.setStyleSheet("""
            QSpinBox {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
                color: #2c3e50;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
            }
        """)
        product_layout.addWidget(self.quantity_spinbox, 3, 1)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        self.add_button = QPushButton("➕ إضافة")
        self.add_button.setEnabled(False)
        self.add_button.clicked.connect(self.add_to_cart)
        
        self.clear_button = QPushButton("🗑️ مسح")
        self.clear_button.clicked.connect(self.clear_selection)
        
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.clear_button)
        
        # إضافة المجموعات إلى اللوحة
        layout.addWidget(search_group)
        layout.addWidget(product_group)
        layout.addLayout(buttons_layout)
        
        # أزرار العمليات السريعة
        quick_actions_group = QGroupBox("عمليات سريعة")
        quick_actions_group.setStyleSheet(search_group.styleSheet())
        quick_layout = QVBoxLayout(quick_actions_group)
        
        new_sale_btn = QPushButton("🆕 بيع جديد")
        new_sale_btn.clicked.connect(self.new_sale)
        
        hold_sale_btn = QPushButton("⏸️ تعليق البيع")
        hold_sale_btn.clicked.connect(self.hold_sale)
        
        recall_sale_btn = QPushButton("📋 استدعاء بيع")
        recall_sale_btn.clicked.connect(self.recall_sale)
        
        quick_layout.addWidget(new_sale_btn)
        quick_layout.addWidget(hold_sale_btn)
        quick_layout.addWidget(recall_sale_btn)
        
        layout.addWidget(quick_actions_group)
        layout.addStretch()
        
        return panel
    
    def create_right_panel(self):
        """إنشاء اللوحة اليمنى"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 15px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # عنوان الفاتورة
        invoice_header = QHBoxLayout()
        
        title = QLabel("🧾 الفاتورة الحالية")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50;")
        
        self.invoice_number_label = QLabel("رقم الفاتورة: جديدة")
        self.invoice_number_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")
        
        invoice_header.addWidget(title)
        invoice_header.addStretch()
        invoice_header.addWidget(self.invoice_number_label)
        
        layout.addLayout(invoice_header)
        
        # جدول عناصر الفاتورة
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels([
            "المنتج", "السعر", "الكمية", "الخصم", "المجموع", "حذف"
        ])
        
        # تنسيق الجدول
        self.items_table.setStyleSheet("""
            QTableWidget {
                background-color: #e0e5ec;
                border: none;
                border-radius: 10px;
                gridline-color: #bdc3c7;
                selection-background-color: #3498db;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        
        # إعدادات الجدول
        header = self.items_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # اسم المنتج
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # الكمية
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # الخصم
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # المجموع
        header.setSectionResizeMode(5, QHeaderView.Fixed)    # حذف
        
        self.items_table.setColumnWidth(1, 80)   # السعر
        self.items_table.setColumnWidth(2, 60)   # الكمية
        self.items_table.setColumnWidth(3, 60)   # الخصم
        self.items_table.setColumnWidth(4, 100)  # المجموع
        self.items_table.setColumnWidth(5, 60)   # حذف
        
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.items_table)
        
        # مجاميع الفاتورة
        totals_frame = self.create_totals_frame()
        layout.addWidget(totals_frame)
        
        # أزرار إتمام البيع
        payment_frame = self.create_payment_frame()
        layout.addWidget(payment_frame)
        
        return panel
    
    def create_totals_frame(self):
        """إنشاء إطار المجاميع"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QGridLayout(frame)
        
        # تسميات المجاميع
        labels_style = "color: white; font-weight: bold; font-size: 14px;"
        values_style = "color: #2ecc71; font-weight: bold; font-size: 16px;"
        
        # المجموع الفرعي
        layout.addWidget(QLabel("المجموع الفرعي:"), 0, 0)
        self.subtotal_label = QLabel("0.00 دج")
        self.subtotal_label.setStyleSheet(values_style)
        self.subtotal_label.setAlignment(Qt.AlignLeft)
        layout.addWidget(self.subtotal_label, 0, 1)
        
        # الخصم
        layout.addWidget(QLabel("الخصم:"), 1, 0)
        self.discount_label = QLabel("0.00 دج")
        self.discount_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 16px;")
        self.discount_label.setAlignment(Qt.AlignLeft)
        layout.addWidget(self.discount_label, 1, 1)
        
        # الضريبة
        layout.addWidget(QLabel("الضريبة (19%):"), 2, 0)
        self.tax_label = QLabel("0.00 دج")
        self.tax_label.setStyleSheet(values_style)
        self.tax_label.setAlignment(Qt.AlignLeft)
        layout.addWidget(self.tax_label, 2, 1)
        
        # المجموع النهائي
        layout.addWidget(QLabel("المجموع النهائي:"), 3, 0)
        self.total_label = QLabel("0.00 دج")
        self.total_label.setStyleSheet("color: #f39c12; font-weight: bold; font-size: 20px;")
        self.total_label.setAlignment(Qt.AlignLeft)
        layout.addWidget(self.total_label, 3, 1)
        
        # تطبيق التنسيق على جميع التسميات
        for i in range(4):
            label = layout.itemAtPosition(i, 0).widget()
            label.setStyleSheet(labels_style)
        
        return frame
    
    def create_payment_frame(self):
        """إنشاء إطار الدفع"""
        frame = QFrame()
        layout = QVBoxLayout(frame)
        
        # أزرار الدفع
        payment_layout = QHBoxLayout()
        
        self.cash_button = QPushButton("💵 دفع نقدي")
        self.cash_button.setEnabled(False)
        self.cash_button.clicked.connect(self.process_cash_payment)
        self.cash_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        
        self.card_button = QPushButton("💳 دفع بالبطاقة")
        self.card_button.setEnabled(False)
        self.card_button.clicked.connect(self.process_card_payment)
        self.card_button.setStyleSheet(self.cash_button.styleSheet().replace("#27ae60", "#3498db").replace("#229954", "#2980b9"))
        
        payment_layout.addWidget(self.cash_button)
        payment_layout.addWidget(self.card_button)
        
        # أزرار إضافية
        additional_layout = QHBoxLayout()
        
        self.discount_button = QPushButton("🏷️ خصم")
        self.discount_button.clicked.connect(self.apply_discount)
        
        self.clear_cart_button = QPushButton("🗑️ مسح الكل")
        self.clear_cart_button.clicked.connect(self.clear_cart)
        self.clear_cart_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        additional_layout.addWidget(self.discount_button)
        additional_layout.addWidget(self.clear_cart_button)
        
        layout.addLayout(payment_layout)
        layout.addLayout(additional_layout)
        
        return frame

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.barcode_input.returnPressed.connect(self.search_product)
        self.quantity_spinbox.valueChanged.connect(self.update_add_button_state)

    def setup_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # F1 - بيع جديد
        QShortcut(QKeySequence("F1"), self, self.new_sale)

        # F2 - بحث منتج
        QShortcut(QKeySequence("F2"), self, lambda: self.barcode_input.setFocus())

        # F3 - إضافة إلى السلة
        QShortcut(QKeySequence("F3"), self, self.add_to_cart)

        # F4 - دفع نقدي
        QShortcut(QKeySequence("F4"), self, self.process_cash_payment)

        # F5 - دفع بالبطاقة
        QShortcut(QKeySequence("F5"), self, self.process_card_payment)

        # Delete - مسح السلة
        QShortcut(QKeySequence("Delete"), self, self.clear_cart)

    def search_product(self):
        """البحث عن منتج بالباركود"""
        barcode = self.barcode_input.text().strip()

        if not barcode:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الباركود")
            return

        # البحث في قاعدة البيانات
        product = self.db_manager.get_product_by_barcode(barcode)

        if product:
            self.display_product_info(product)
            self.current_product = product
            self.add_button.setEnabled(True)
            self.quantity_spinbox.setFocus()
        else:
            QMessageBox.warning(self, "منتج غير موجود",
                              f"لم يتم العثور على منتج بالباركود: {barcode}")
            self.clear_selection()

    def display_product_info(self, product):
        """عرض معلومات المنتج"""
        self.product_name_label.setText(product['name'])
        self.product_name_label.setStyleSheet("color: #2c3e50; font-weight: bold;")

        self.product_price_label.setText(f"{product['selling_price']:.2f} دج")

        stock = product['stock_quantity']
        self.product_stock_label.setText(str(stock))

        # تغيير لون المخزون حسب الكمية
        if stock <= 0:
            self.product_stock_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        elif stock <= product.get('min_stock_level', 5):
            self.product_stock_label.setStyleSheet("color: #f39c12; font-weight: bold;")
        else:
            self.product_stock_label.setStyleSheet("color: #27ae60; font-weight: bold;")

        # تحديد الحد الأقصى للكمية
        self.quantity_spinbox.setMaximum(max(1, stock))

    def clear_selection(self):
        """مسح الاختيار الحالي"""
        self.barcode_input.clear()
        self.product_name_label.setText("لم يتم اختيار منتج")
        self.product_name_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        self.product_price_label.setText("0.00 دج")
        self.product_stock_label.setText("0")
        self.quantity_spinbox.setValue(1)
        self.add_button.setEnabled(False)
        self.current_product = None
        self.barcode_input.setFocus()

    def update_add_button_state(self):
        """تحديث حالة زر الإضافة"""
        if hasattr(self, 'current_product') and self.current_product:
            quantity = self.quantity_spinbox.value()
            stock = self.current_product['stock_quantity']
            self.add_button.setEnabled(quantity <= stock and quantity > 0)

    def add_to_cart(self):
        """إضافة المنتج إلى السلة"""
        if not hasattr(self, 'current_product') or not self.current_product:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج أولاً")
            return

        quantity = self.quantity_spinbox.value()
        product = self.current_product

        # التحقق من توفر الكمية
        if quantity > product['stock_quantity']:
            QMessageBox.warning(self, "مخزون غير كافي",
                              f"الكمية المتاحة: {product['stock_quantity']}")
            return

        # التحقق من وجود المنتج في السلة مسبقاً
        existing_row = self.find_product_in_cart(product['id'])

        if existing_row >= 0:
            # تحديث الكمية الموجودة
            current_qty = int(self.items_table.item(existing_row, 2).text())
            new_qty = current_qty + quantity

            if new_qty > product['stock_quantity']:
                QMessageBox.warning(self, "مخزون غير كافي",
                                  f"الكمية الإجمالية ستكون {new_qty} والمتاح {product['stock_quantity']}")
                return

            self.update_cart_item(existing_row, new_qty)
        else:
            # إضافة منتج جديد
            self.add_new_cart_item(product, quantity)

        # تحديث المجاميع
        self.update_totals()

        # مسح الاختيار والعودة للبحث
        self.clear_selection()

    def find_product_in_cart(self, product_id):
        """البحث عن منتج في السلة"""
        for row in range(self.items_table.rowCount()):
            item_data = self.items_table.item(row, 0).data(Qt.UserRole)
            if item_data and item_data.get('product_id') == product_id:
                return row
        return -1

    def add_new_cart_item(self, product, quantity):
        """إضافة عنصر جديد للسلة"""
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)

        unit_price = product['selling_price']
        total_price = unit_price * quantity

        # اسم المنتج
        name_item = QTableWidgetItem(product['name'])
        name_item.setData(Qt.UserRole, {
            'product_id': product['id'],
            'barcode': product['barcode'],
            'unit_price': unit_price
        })
        self.items_table.setItem(row, 0, name_item)

        # السعر
        price_item = QTableWidgetItem(f"{unit_price:.2f}")
        price_item.setTextAlignment(Qt.AlignCenter)
        self.items_table.setItem(row, 1, price_item)

        # الكمية
        qty_item = QTableWidgetItem(str(quantity))
        qty_item.setTextAlignment(Qt.AlignCenter)
        self.items_table.setItem(row, 2, qty_item)

        # الخصم
        discount_item = QTableWidgetItem("0.00")
        discount_item.setTextAlignment(Qt.AlignCenter)
        self.items_table.setItem(row, 3, discount_item)

        # المجموع
        total_item = QTableWidgetItem(f"{total_price:.2f}")
        total_item.setTextAlignment(Qt.AlignCenter)
        self.items_table.setItem(row, 4, total_item)

        # زر الحذف
        delete_button = QPushButton("🗑️")
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 5px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_button.clicked.connect(lambda: self.remove_cart_item(row))
        self.items_table.setCellWidget(row, 5, delete_button)

        # إضافة إلى قائمة العناصر
        self.current_sale_items.append({
            'product_id': product['id'],
            'name': product['name'],
            'quantity': quantity,
            'unit_price': unit_price,
            'total_price': total_price,
            'discount_amount': 0.0
        })

    def update_cart_item(self, row, new_quantity):
        """تحديث عنصر في السلة"""
        # الحصول على بيانات المنتج
        name_item = self.items_table.item(row, 0)
        product_data = name_item.data(Qt.UserRole)
        unit_price = product_data['unit_price']

        # تحديث الكمية والمجموع
        new_total = unit_price * new_quantity

        self.items_table.item(row, 2).setText(str(new_quantity))
        self.items_table.item(row, 4).setText(f"{new_total:.2f}")

        # تحديث قائمة العناصر
        for item in self.current_sale_items:
            if item['product_id'] == product_data['product_id']:
                item['quantity'] = new_quantity
                item['total_price'] = new_total
                break

    def remove_cart_item(self, row):
        """حذف عنصر من السلة"""
        if row < self.items_table.rowCount():
            # الحصول على معرف المنتج
            name_item = self.items_table.item(row, 0)
            product_data = name_item.data(Qt.UserRole)
            product_id = product_data['product_id']

            # حذف من الجدول
            self.items_table.removeRow(row)

            # حذف من قائمة العناصر
            self.current_sale_items = [
                item for item in self.current_sale_items
                if item['product_id'] != product_id
            ]

            # تحديث المجاميع
            self.update_totals()

            # تحديث أزرار الحذف
            self.update_delete_buttons()

    def update_delete_buttons(self):
        """تحديث أزرار الحذف بعد حذف صف"""
        for row in range(self.items_table.rowCount()):
            delete_button = self.items_table.cellWidget(row, 5)
            if delete_button:
                # إعادة ربط الإشارة بالصف الصحيح
                delete_button.clicked.disconnect()
                delete_button.clicked.connect(lambda checked, r=row: self.remove_cart_item(r))

    def update_totals(self):
        """تحديث المجاميع"""
        subtotal = sum(item['total_price'] for item in self.current_sale_items)
        discount = sum(item['discount_amount'] for item in self.current_sale_items)
        tax_rate = 0.19  # 19% ضريبة
        tax_amount = (subtotal - discount) * tax_rate
        total = subtotal - discount + tax_amount

        self.subtotal_label.setText(f"{subtotal:.2f} دج")
        self.discount_label.setText(f"{discount:.2f} دج")
        self.tax_label.setText(f"{tax_amount:.2f} دج")
        self.total_label.setText(f"{total:.2f} دج")

        self.current_total = total

        # تفعيل أزرار الدفع إذا كان هناك عناصر
        has_items = len(self.current_sale_items) > 0
        self.cash_button.setEnabled(has_items)
        self.card_button.setEnabled(has_items)

    def clear_cart(self):
        """مسح جميع عناصر السلة"""
        reply = QMessageBox.question(
            self, "مسح السلة",
            "هل أنت متأكد من مسح جميع العناصر؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.items_table.setRowCount(0)
            self.current_sale_items.clear()
            self.update_totals()
            self.clear_selection()

    def new_sale(self):
        """بدء بيع جديد"""
        if len(self.current_sale_items) > 0:
            reply = QMessageBox.question(
                self, "بيع جديد",
                "هناك عناصر في السلة الحالية. هل تريد المتابعة؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.No:
                return

        self.clear_cart()
        self.invoice_number_label.setText("رقم الفاتورة: جديدة")

    def apply_discount(self):
        """تطبيق خصم"""
        # سيتم تنفيذها لاحقاً
        QMessageBox.information(self, "الخصم", "ميزة الخصم قيد التطوير")

    def hold_sale(self):
        """تعليق البيع"""
        # سيتم تنفيذها لاحقاً
        QMessageBox.information(self, "تعليق البيع", "ميزة تعليق البيع قيد التطوير")

    def recall_sale(self):
        """استدعاء بيع معلق"""
        # سيتم تنفيذها لاحقاً
        QMessageBox.information(self, "استدعاء البيع", "ميزة استدعاء البيع قيد التطوير")

    def process_cash_payment(self):
        """معالجة الدفع النقدي"""
        if len(self.current_sale_items) == 0:
            QMessageBox.warning(self, "تحذير", "لا توجد عناصر في السلة")
            return

        # عرض نافذة الدفع النقدي
        from .payment_dialog import CashPaymentDialog
        dialog = CashPaymentDialog(self.current_total, self)

        if dialog.exec_() == dialog.Accepted:
            payment_data = dialog.get_payment_data()
            self.complete_sale(payment_data)

    def process_card_payment(self):
        """معالجة الدفع بالبطاقة"""
        if len(self.current_sale_items) == 0:
            QMessageBox.warning(self, "تحذير", "لا توجد عناصر في السلة")
            return

        # تأكيد الدفع بالبطاقة
        reply = QMessageBox.question(
            self, "الدفع بالبطاقة",
            f"المبلغ المطلوب: {self.current_total:.2f} دج\nهل تم الدفع بنجاح؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            payment_data = {
                'method': 'بطاقة',
                'amount': self.current_total,
                'paid_amount': self.current_total,
                'change': 0.0
            }
            self.complete_sale(payment_data)

    def complete_sale(self, payment_data):
        """إتمام عملية البيع"""
        try:
            # إعداد بيانات البيع
            sale_data = {
                'user_id': self.user_data['id'],
                'total_amount': self.current_total,
                'paid_amount': payment_data['paid_amount'],
                'change_amount': payment_data.get('change', 0.0),
                'payment_method': payment_data['method']
            }

            # حفظ البيع في قاعدة البيانات
            invoice_number = self.db_manager.create_sale(sale_data, self.current_sale_items)

            if invoice_number:
                # عرض رسالة نجاح
                QMessageBox.information(
                    self, "تم البيع بنجاح",
                    f"رقم الفاتورة: {invoice_number}\nالمبلغ: {self.current_total:.2f} دج"
                )

                # طباعة الفاتورة (اختياري)
                self.print_receipt(invoice_number)

                # بدء بيع جديد
                self.new_sale()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ البيع")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إتمام البيع: {str(e)}")

    def print_receipt(self, invoice_number):
        """طباعة الفاتورة"""
        # سيتم تنفيذها لاحقاً مع نظام الطباعة
        reply = QMessageBox.question(
            self, "طباعة الفاتورة",
            "هل تريد طباعة الفاتورة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "الطباعة", "تم إرسال الفاتورة للطباعة")
