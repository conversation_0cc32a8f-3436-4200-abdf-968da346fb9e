/**
 * الأدوات المساعدة
 * Utility Functions
 */

export class Utils {
    constructor() {
        this.arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        this.englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    }

    /**
     * تشفير كلمة المرور (SHA-256 بسيط)
     */
    async hashPassword(password) {
        try {
            // استخدام Web Crypto API إذا كان متاحاً
            if (window.crypto && window.crypto.subtle) {
                const encoder = new TextEncoder();
                const data = encoder.encode(password + 'pos_salt_2024');
                const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
                const hashArray = Array.from(new Uint8Array(hashBuffer));
                return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
            } else {
                // تشفير بسيط كبديل
                return this.simpleHash(password + 'pos_salt_2024');
            }
        } catch (error) {
            console.error('خطأ في تشفير كلمة المرور:', error);
            return this.simpleHash(password + 'pos_salt_2024');
        }
    }

    /**
     * تشفير بسيط كبديل
     */
    simpleHash(str) {
        let hash = 0;
        if (str.length === 0) return hash.toString();
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        
        return Math.abs(hash).toString(16);
    }

    /**
     * تنسيق الأرقام بالفواصل
     */
    formatNumber(number, decimals = 2) {
        if (isNaN(number)) return '0.00';
        
        const num = parseFloat(number);
        return new Intl.NumberFormat('ar-SA', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(num);
    }

    /**
     * تنسيق العملة
     */
    formatCurrency(amount, currency = 'ريال سعودي') {
        if (isNaN(amount)) return '0.00 ريال سعودي';
        
        const formatted = this.formatNumber(amount, 2);
        return `${formatted} ${currency}`;
    }

    /**
     * تحويل الأرقام الإنجليزية إلى عربية
     */
    toArabicNumbers(str) {
        if (!str) return str;
        
        return str.toString().replace(/[0-9]/g, (match) => {
            return this.arabicNumbers[parseInt(match)];
        });
    }

    /**
     * تحويل الأرقام العربية إلى إنجليزية
     */
    toEnglishNumbers(str) {
        if (!str) return str;
        
        return str.toString().replace(/[٠-٩]/g, (match) => {
            return this.englishNumbers[this.arabicNumbers.indexOf(match)];
        });
    }

    /**
     * تنسيق التاريخ
     */
    formatDate(date, format = 'DD/MM/YYYY') {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const day = d.getDate().toString().padStart(2, '0');
        const month = (d.getMonth() + 1).toString().padStart(2, '0');
        const year = d.getFullYear();
        const hours = d.getHours().toString().padStart(2, '0');
        const minutes = d.getMinutes().toString().padStart(2, '0');
        
        switch (format) {
            case 'DD/MM/YYYY':
                return `${day}/${month}/${year}`;
            case 'DD/MM/YYYY HH:mm':
                return `${day}/${month}/${year} ${hours}:${minutes}`;
            case 'YYYY-MM-DD':
                return `${year}-${month}-${day}`;
            case 'relative':
                return this.getRelativeTime(d);
            default:
                return d.toLocaleDateString('ar-SA');
        }
    }

    /**
     * الحصول على الوقت النسبي
     */
    getRelativeTime(date) {
        const now = new Date();
        const diff = now - new Date(date);
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (seconds < 60) return 'الآن';
        if (minutes < 60) return `منذ ${minutes} دقيقة`;
        if (hours < 24) return `منذ ${hours} ساعة`;
        if (days < 7) return `منذ ${days} يوم`;
        
        return this.formatDate(date);
    }

    /**
     * توليد معرف فريد
     */
    generateId(prefix = '') {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        return `${prefix}${timestamp}_${random}`;
    }

    /**
     * توليد باركود
     */
    generateBarcode() {
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
        return timestamp.substr(-7) + random;
    }

    /**
     * التحقق من صحة البريد الإلكتروني
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * التحقق من صحة رقم الهاتف السعودي
     */
    isValidSaudiPhone(phone) {
        const phoneRegex = /^(05|5)[0-9]{8}$/;
        return phoneRegex.test(phone.replace(/\s+/g, ''));
    }

    /**
     * تنسيق رقم الهاتف
     */
    formatPhone(phone) {
        if (!phone) return '';
        
        const cleaned = phone.replace(/\D/g, '');
        
        if (cleaned.length === 10 && cleaned.startsWith('05')) {
            return `${cleaned.substr(0, 3)} ${cleaned.substr(3, 3)} ${cleaned.substr(6, 4)}`;
        }
        
        return phone;
    }

    /**
     * تنظيف النص
     */
    sanitizeText(text) {
        if (!text) return '';
        
        return text
            .trim()
            .replace(/\s+/g, ' ')
            .replace(/[<>]/g, '');
    }

    /**
     * البحث في النص العربي
     */
    arabicSearch(text, query) {
        if (!text || !query) return false;
        
        const normalizedText = this.normalizeArabic(text.toLowerCase());
        const normalizedQuery = this.normalizeArabic(query.toLowerCase());
        
        return normalizedText.includes(normalizedQuery);
    }

    /**
     * تطبيع النص العربي للبحث
     */
    normalizeArabic(text) {
        return text
            .replace(/[أإآ]/g, 'ا')
            .replace(/[ة]/g, 'ه')
            .replace(/[ى]/g, 'ي')
            .replace(/[ؤ]/g, 'و')
            .replace(/[ئ]/g, 'ي')
            .replace(/[ً]/g, '')
            .replace(/[ٌ]/g, '')
            .replace(/[ٍ]/g, '')
            .replace(/[َ]/g, '')
            .replace(/[ُ]/g, '')
            .replace(/[ِ]/g, '')
            .replace(/[ّ]/g, '')
            .replace(/[ْ]/g, '');
    }

    /**
     * حساب النسبة المئوية
     */
    calculatePercentage(value, total) {
        if (!total || total === 0) return 0;
        return (value / total) * 100;
    }

    /**
     * حساب الخصم
     */
    calculateDiscount(price, discountPercent) {
        if (!price || !discountPercent) return 0;
        return (price * discountPercent) / 100;
    }

    /**
     * حساب الضريبة
     */
    calculateTax(amount, taxRate = 0.15) {
        if (!amount) return 0;
        return amount * taxRate;
    }

    /**
     * تحويل الحجم بالبايت إلى وحدة قابلة للقراءة
     */
    formatBytes(bytes, decimals = 2) {
        if (bytes === 0) return '0 بايت';
        
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }

    /**
     * نسخ النص إلى الحافظة
     */
    async copyToClipboard(text) {
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text);
                return true;
            } else {
                // طريقة بديلة للمتصفحات القديمة
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                const result = document.execCommand('copy');
                document.body.removeChild(textArea);
                return result;
            }
        } catch (error) {
            console.error('خطأ في نسخ النص:', error);
            return false;
        }
    }

    /**
     * تحميل ملف
     */
    downloadFile(data, filename, type = 'application/json') {
        const blob = new Blob([data], { type });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
    }

    /**
     * قراءة ملف
     */
    readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            
            reader.readAsText(file);
        });
    }

    /**
     * تأخير التنفيذ
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * إزالة التكرار من المصفوفة
     */
    uniqueArray(array, key = null) {
        if (!Array.isArray(array)) return [];
        
        if (key) {
            const seen = new Set();
            return array.filter(item => {
                const value = item[key];
                if (seen.has(value)) {
                    return false;
                }
                seen.add(value);
                return true;
            });
        }
        
        return [...new Set(array)];
    }

    /**
     * ترتيب المصفوفة
     */
    sortArray(array, key, direction = 'asc') {
        if (!Array.isArray(array)) return [];
        
        return array.sort((a, b) => {
            let aVal = key ? a[key] : a;
            let bVal = key ? b[key] : b;
            
            // معالجة النصوص العربية
            if (typeof aVal === 'string' && typeof bVal === 'string') {
                aVal = this.normalizeArabic(aVal);
                bVal = this.normalizeArabic(bVal);
            }
            
            if (direction === 'asc') {
                return aVal > bVal ? 1 : -1;
            } else {
                return aVal < bVal ? 1 : -1;
            }
        });
    }

    /**
     * تصفية المصفوفة
     */
    filterArray(array, filters) {
        if (!Array.isArray(array) || !filters) return array;
        
        return array.filter(item => {
            return Object.keys(filters).every(key => {
                const filterValue = filters[key];
                const itemValue = item[key];
                
                if (filterValue === null || filterValue === undefined || filterValue === '') {
                    return true;
                }
                
                if (typeof filterValue === 'string' && typeof itemValue === 'string') {
                    return this.arabicSearch(itemValue, filterValue);
                }
                
                return itemValue === filterValue;
            });
        });
    }

    /**
     * تجميع البيانات
     */
    groupBy(array, key) {
        if (!Array.isArray(array)) return {};
        
        return array.reduce((groups, item) => {
            const group = item[key];
            if (!groups[group]) {
                groups[group] = [];
            }
            groups[group].push(item);
            return groups;
        }, {});
    }

    /**
     * حساب المجموع
     */
    sum(array, key = null) {
        if (!Array.isArray(array)) return 0;
        
        return array.reduce((total, item) => {
            const value = key ? item[key] : item;
            return total + (parseFloat(value) || 0);
        }, 0);
    }

    /**
     * حساب المتوسط
     */
    average(array, key = null) {
        if (!Array.isArray(array) || array.length === 0) return 0;
        
        const total = this.sum(array, key);
        return total / array.length;
    }

    /**
     * إنشاء لون عشوائي
     */
    randomColor() {
        const colors = [
            '#3498db', '#e74c3c', '#2ecc71', '#f39c12',
            '#9b59b6', '#1abc9c', '#34495e', '#e67e22'
        ];
        
        return colors[Math.floor(Math.random() * colors.length)];
    }

    /**
     * التحقق من كون الجهاز محمول
     */
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * التحقق من كون الجهاز لوحي
     */
    isTablet() {
        return /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent);
    }

    /**
     * الحصول على معلومات الجهاز
     */
    getDeviceInfo() {
        return {
            isMobile: this.isMobile(),
            isTablet: this.isTablet(),
            isDesktop: !this.isMobile() && !this.isTablet(),
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            screenWidth: window.screen.width,
            screenHeight: window.screen.height,
            windowWidth: window.innerWidth,
            windowHeight: window.innerHeight
        };
    }

    /**
     * تشغيل صوت تنبيه
     */
    playSound(type = 'success') {
        try {
            // إنشاء صوت بسيط باستخدام Web Audio API
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            // تحديد نوع الصوت
            switch (type) {
                case 'success':
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
                    break;
                case 'error':
                    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
                    oscillator.frequency.setValueAtTime(200, audioContext.currentTime + 0.1);
                    break;
                case 'warning':
                    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
                    break;
                default:
                    oscillator.frequency.setValueAtTime(500, audioContext.currentTime);
            }
            
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
            
        } catch (error) {
            console.log('لا يمكن تشغيل الصوت:', error);
        }
    }
}
