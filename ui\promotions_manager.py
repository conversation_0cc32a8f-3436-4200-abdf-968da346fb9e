#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير العروض والخصومات - Promotions Manager
نظام متقدم لإدارة العروض والخصومات والحملات التسويقية
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget, 
                            QTableWidgetItem, QFrame, QComboBox, QSpinBox,
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QDoubleSpinBox, QTextEdit, QGroupBox, QTabWidget,
                            QDateEdit, QCheckBox, QProgressBar, QSlider,
                            QRadioButton, QButtonGroup)
from PyQt5.QtCore import Qt, pyqtSignal, <PERSON><PERSON><PERSON>, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette
from datetime import datetime, timedelta

class PromotionCard(QFrame):
    """بطاقة عرض ترويجي"""
    
    def __init__(self, promotion_data, parent=None):
        super().__init__(parent)
        self.promotion_data = promotion_data
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة البطاقة"""
        self.setFixedSize(300, 200)
        self.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 15px;
                margin: 10px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # عنوان العرض
        title = QLabel(self.promotion_data.get('title', 'عرض جديد'))
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #2c3e50;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # نوع العرض
        promo_type = QLabel(f"🏷️ {self.promotion_data.get('type', 'خصم')}")
        promo_type.setStyleSheet("color: #3498db; font-weight: bold;")
        promo_type.setAlignment(Qt.AlignCenter)
        layout.addWidget(promo_type)
        
        # قيمة العرض
        value = self.promotion_data.get('value', 0)
        value_type = self.promotion_data.get('value_type', 'percentage')
        
        if value_type == 'percentage':
            value_text = f"{value}% خصم"
            color = "#e74c3c"
        else:
            value_text = f"{value} دج خصم"
            color = "#27ae60"
        
        value_label = QLabel(value_text)
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setStyleSheet(f"color: {color};")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # فترة العرض
        start_date = self.promotion_data.get('start_date', datetime.now())
        end_date = self.promotion_data.get('end_date', datetime.now() + timedelta(days=7))
        
        period_label = QLabel(f"📅 من {start_date.strftime('%Y/%m/%d')} إلى {end_date.strftime('%Y/%m/%d')}")
        period_label.setStyleSheet("color: #7f8c8d; font-size: 10px;")
        period_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(period_label)
        
        # حالة العرض
        status = self.promotion_data.get('status', 'نشط')
        status_color = "#27ae60" if status == "نشط" else "#e74c3c"
        
        status_label = QLabel(f"● {status}")
        status_label.setStyleSheet(f"color: {status_color}; font-weight: bold;")
        status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(status_label)
        
        # أزرار العمل
        buttons_layout = QHBoxLayout()
        
        edit_btn = QPushButton("✏️")
        edit_btn.setMaximumSize(30, 30)
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 15px;
                font-weight: bold;
            }
        """)
        
        delete_btn = QPushButton("🗑️")
        delete_btn.setMaximumSize(30, 30)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 15px;
                font-weight: bold;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(edit_btn)
        buttons_layout.addWidget(delete_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)

class PromotionsManager(QWidget):
    """مدير العروض والخصومات الرئيسي"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.promotions = []
        self.setup_ui()
        self.load_promotions()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان الصفحة
        title = QLabel("🎯 إدارة العروض والخصومات")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #e0e5ec;
                border-radius: 10px;
            }
            QTabBar::tab {
                background-color: #e0e5ec;
                color: #2c3e50;
                padding: 12px 20px;
                margin: 2px;
                border-radius: 8px;
                font-weight: bold;
                box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        # تبويب العروض النشطة
        active_promotions_tab = self.create_active_promotions_tab()
        self.tab_widget.addTab(active_promotions_tab, "🎯 العروض النشطة")
        
        # تبويب إنشاء عرض جديد
        new_promotion_tab = self.create_new_promotion_tab()
        self.tab_widget.addTab(new_promotion_tab, "➕ عرض جديد")
        
        # تبويب قوالب العروض
        templates_tab = self.create_templates_tab()
        self.tab_widget.addTab(templates_tab, "📋 قوالب العروض")
        
        # تبويب إحصائيات العروض
        analytics_tab = self.create_analytics_tab()
        self.tab_widget.addTab(analytics_tab, "📊 إحصائيات العروض")
        
        main_layout.addWidget(self.tab_widget)
    
    def create_active_promotions_tab(self):
        """إنشاء تبويب العروض النشطة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        # فلتر العروض
        filter_label = QLabel("فلترة:")
        filter_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
        toolbar_layout.addWidget(filter_label)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع العروض", "نشط", "منتهي", "مجدول", "متوقف"])
        self.status_filter.currentTextChanged.connect(self.filter_promotions)
        toolbar_layout.addWidget(self.status_filter)
        
        toolbar_layout.addStretch()
        
        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.load_promotions)
        toolbar_layout.addWidget(refresh_btn)
        
        layout.addLayout(toolbar_layout)
        
        # منطقة العروض
        self.promotions_scroll_area = self.create_promotions_grid()
        layout.addWidget(self.promotions_scroll_area)
        
        return widget
    
    def create_promotions_grid(self):
        """إنشاء شبكة العروض"""
        from PyQt5.QtWidgets import QScrollArea
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # إطار العروض
        self.promotions_frame = QWidget()
        self.promotions_layout = QGridLayout(self.promotions_frame)
        self.promotions_layout.setSpacing(15)
        
        scroll_area.setWidget(self.promotions_frame)
        
        return scroll_area
    
    def create_new_promotion_tab(self):
        """إنشاء تبويب العرض الجديد"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setSpacing(20)
        
        # الجانب الأيسر - نموذج العرض
        left_panel = self.create_promotion_form()
        layout.addWidget(left_panel, 2)
        
        # الجانب الأيمن - معاينة العرض
        right_panel = self.create_promotion_preview()
        layout.addWidget(right_panel, 1)
        
        return widget
    
    def create_promotion_form(self):
        """إنشاء نموذج العرض"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # عنوان
        title = QLabel("➕ إنشاء عرض ترويجي جديد")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # معلومات أساسية
        basic_info_group = QGroupBox("المعلومات الأساسية")
        basic_info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)
        
        basic_layout = QGridLayout(basic_info_group)
        
        # اسم العرض
        basic_layout.addWidget(QLabel("اسم العرض: *"), 0, 0)
        self.promo_name_input = QLineEdit()
        self.promo_name_input.setPlaceholderText("أدخل اسم العرض")
        self.promo_name_input.textChanged.connect(self.update_preview)
        basic_layout.addWidget(self.promo_name_input, 0, 1)
        
        # وصف العرض
        basic_layout.addWidget(QLabel("الوصف:"), 1, 0)
        self.promo_description = QTextEdit()
        self.promo_description.setMaximumHeight(80)
        self.promo_description.setPlaceholderText("وصف العرض")
        basic_layout.addWidget(self.promo_description, 1, 1)
        
        layout.addWidget(basic_info_group)
        
        # نوع العرض
        type_group = QGroupBox("نوع العرض")
        type_group.setStyleSheet(basic_info_group.styleSheet())
        type_layout = QVBoxLayout(type_group)
        
        self.promo_type_group = QButtonGroup()
        
        # خصم بالنسبة المئوية
        self.percentage_radio = QRadioButton("خصم بالنسبة المئوية")
        self.percentage_radio.setChecked(True)
        self.percentage_radio.toggled.connect(self.on_promo_type_changed)
        self.promo_type_group.addButton(self.percentage_radio)
        type_layout.addWidget(self.percentage_radio)
        
        # خصم بمبلغ ثابت
        self.fixed_amount_radio = QRadioButton("خصم بمبلغ ثابت")
        self.fixed_amount_radio.toggled.connect(self.on_promo_type_changed)
        self.promo_type_group.addButton(self.fixed_amount_radio)
        type_layout.addWidget(self.fixed_amount_radio)
        
        # اشتر واحصل على آخر مجاناً
        self.buy_get_radio = QRadioButton("اشتر X واحصل على Y مجاناً")
        self.buy_get_radio.toggled.connect(self.on_promo_type_changed)
        self.promo_type_group.addButton(self.buy_get_radio)
        type_layout.addWidget(self.buy_get_radio)
        
        layout.addWidget(type_group)
        
        # قيمة العرض
        value_group = QGroupBox("قيمة العرض")
        value_group.setStyleSheet(basic_info_group.styleSheet())
        value_layout = QGridLayout(value_group)
        
        value_layout.addWidget(QLabel("القيمة:"), 0, 0)
        self.promo_value_input = QDoubleSpinBox()
        self.promo_value_input.setRange(0, 100)
        self.promo_value_input.setSuffix("%")
        self.promo_value_input.valueChanged.connect(self.update_preview)
        value_layout.addWidget(self.promo_value_input, 0, 1)
        
        # الحد الأدنى للشراء
        value_layout.addWidget(QLabel("الحد الأدنى للشراء:"), 1, 0)
        self.min_purchase_input = QDoubleSpinBox()
        self.min_purchase_input.setRange(0, 999999)
        self.min_purchase_input.setSuffix(" دج")
        value_layout.addWidget(self.min_purchase_input, 1, 1)
        
        # الحد الأقصى للخصم
        value_layout.addWidget(QLabel("الحد الأقصى للخصم:"), 2, 0)
        self.max_discount_input = QDoubleSpinBox()
        self.max_discount_input.setRange(0, 999999)
        self.max_discount_input.setSuffix(" دج")
        value_layout.addWidget(self.max_discount_input, 2, 1)
        
        layout.addWidget(value_group)
        
        # فترة العرض
        period_group = QGroupBox("فترة العرض")
        period_group.setStyleSheet(basic_info_group.styleSheet())
        period_layout = QGridLayout(period_group)
        
        period_layout.addWidget(QLabel("تاريخ البداية:"), 0, 0)
        self.start_date_input = QDateEdit()
        self.start_date_input.setDate(QDate.currentDate())
        self.start_date_input.setCalendarPopup(True)
        period_layout.addWidget(self.start_date_input, 0, 1)
        
        period_layout.addWidget(QLabel("تاريخ النهاية:"), 1, 0)
        self.end_date_input = QDateEdit()
        self.end_date_input.setDate(QDate.currentDate().addDays(7))
        self.end_date_input.setCalendarPopup(True)
        period_layout.addWidget(self.end_date_input, 1, 1)
        
        layout.addWidget(period_group)
        
        # تطبيق التنسيق
        input_style = """
            QLineEdit, QTextEdit, QDoubleSpinBox, QDateEdit {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #2c3e50;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
            }
        """
        
        for widget in panel.findChildren((QLineEdit, QTextEdit, QDoubleSpinBox, QDateEdit)):
            widget.setStyleSheet(input_style)
        
        # أزرار العمل
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("💾 حفظ العرض")
        save_btn.clicked.connect(self.save_promotion)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        clear_btn = QPushButton("🗑️ مسح")
        clear_btn.clicked.connect(self.clear_promotion_form)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(clear_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        return panel
    
    def create_promotion_preview(self):
        """إنشاء معاينة العرض"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        
        # عنوان المعاينة
        title = QLabel("👁️ معاينة العرض")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # بطاقة المعاينة
        self.preview_card = PromotionCard({
            'title': 'عرض جديد',
            'type': 'خصم بالنسبة المئوية',
            'value': 0,
            'value_type': 'percentage',
            'start_date': datetime.now(),
            'end_date': datetime.now() + timedelta(days=7),
            'status': 'نشط'
        })
        
        layout.addWidget(self.preview_card, alignment=Qt.AlignCenter)
        layout.addStretch()
        
        return panel

    def create_templates_tab(self):
        """إنشاء تبويب قوالب العروض"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # عنوان
        title = QLabel("📋 قوالب العروض الجاهزة")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # شبكة القوالب
        templates_layout = QGridLayout()

        # قوالب جاهزة
        templates = [
            {
                'name': 'خصم نهاية الأسبوع',
                'description': 'خصم 20% على جميع المنتجات',
                'type': 'percentage',
                'value': 20,
                'icon': '🎉'
            },
            {
                'name': 'عرض الكمية',
                'description': 'اشتر 2 واحصل على الثالث مجاناً',
                'type': 'buy_get',
                'value': '2+1',
                'icon': '🛍️'
            },
            {
                'name': 'خصم العضوية الذهبية',
                'description': 'خصم 15% للعملاء المميزين',
                'type': 'percentage',
                'value': 15,
                'icon': '⭐'
            },
            {
                'name': 'عرض التخليص',
                'description': 'خصم 50% على المنتجات المحددة',
                'type': 'percentage',
                'value': 50,
                'icon': '🔥'
            }
        ]

        for i, template in enumerate(templates):
            template_card = self.create_template_card(template)
            row = i // 2
            col = i % 2
            templates_layout.addWidget(template_card, row, col)

        layout.addLayout(templates_layout)
        layout.addStretch()

        return widget

    def create_template_card(self, template):
        """إنشاء بطاقة قالب"""
        card = QFrame()
        card.setFixedSize(250, 150)
        card.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 15px;
                margin: 10px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)

        layout = QVBoxLayout(card)

        # أيقونة ونوع القالب
        header_layout = QHBoxLayout()

        icon_label = QLabel(template['icon'])
        icon_label.setStyleSheet("font-size: 24px;")
        header_layout.addWidget(icon_label)

        header_layout.addStretch()

        use_btn = QPushButton("استخدام")
        use_btn.clicked.connect(lambda: self.use_template(template))
        use_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 5px 10px;
                font-weight: bold;
            }
        """)
        header_layout.addWidget(use_btn)

        layout.addLayout(header_layout)

        # اسم القالب
        name_label = QLabel(template['name'])
        name_label.setFont(QFont("Arial", 12, QFont.Bold))
        name_label.setStyleSheet("color: #2c3e50;")
        layout.addWidget(name_label)

        # وصف القالب
        desc_label = QLabel(template['description'])
        desc_label.setStyleSheet("color: #7f8c8d; font-size: 10px;")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        return card

    def create_analytics_tab(self):
        """إنشاء تبويب إحصائيات العروض"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # عنوان
        title = QLabel("📊 إحصائيات وتحليلات العروض")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # بطاقات الإحصائيات
        stats_layout = QGridLayout()

        # إجمالي العروض
        total_promos_card = self.create_analytics_card("🎯", "إجمالي العروض", "12", "#3498db")
        stats_layout.addWidget(total_promos_card, 0, 0)

        # العروض النشطة
        active_promos_card = self.create_analytics_card("✅", "العروض النشطة", "5", "#27ae60")
        stats_layout.addWidget(active_promos_card, 0, 1)

        # إجمالي الوفورات
        savings_card = self.create_analytics_card("💰", "إجمالي الوفورات", "25,000 دج", "#e67e22")
        stats_layout.addWidget(savings_card, 0, 2)

        # معدل الاستخدام
        usage_rate_card = self.create_analytics_card("📈", "معدل الاستخدام", "68%", "#9b59b6")
        stats_layout.addWidget(usage_rate_card, 0, 3)

        layout.addLayout(stats_layout)

        # تفاصيل الأداء
        performance_frame = QFrame()
        performance_frame.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)

        performance_layout = QVBoxLayout(performance_frame)

        performance_title = QLabel("📈 أداء العروض")
        performance_title.setFont(QFont("Arial", 14, QFont.Bold))
        performance_title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        performance_layout.addWidget(performance_title)

        # جدول أداء العروض
        performance_table = QTableWidget()
        performance_table.setColumnCount(5)
        performance_table.setHorizontalHeaderLabels([
            "اسم العرض", "عدد الاستخدامات", "إجمالي الوفورات", "معدل التحويل", "التقييم"
        ])

        # بيانات تجريبية
        performance_data = [
            ["خصم نهاية الأسبوع", "45", "9,000 دج", "12%", "⭐⭐⭐⭐"],
            ["عرض الكمية", "32", "6,400 دج", "8%", "⭐⭐⭐⭐⭐"],
            ["خصم العضوية", "28", "5,600 دج", "15%", "⭐⭐⭐"],
            ["عرض التخليص", "67", "13,400 دج", "22%", "⭐⭐⭐⭐⭐"]
        ]

        performance_table.setRowCount(len(performance_data))
        for row, data in enumerate(performance_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                item.setTextAlignment(Qt.AlignCenter)
                performance_table.setItem(row, col, item)

        performance_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: none;
                border-radius: 10px;
                gridline-color: #ecf0f1;
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        header = performance_table.horizontalHeader()
        header.setStretchLastSection(True)
        performance_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        performance_table.setMaximumHeight(200)

        performance_layout.addWidget(performance_table)

        layout.addWidget(performance_frame)

        return widget

    def create_analytics_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFixedSize(200, 100)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 20px; color: white;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("color: white; font-size: 10px; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        layout.addWidget(title_label)

        return card

    # وظائف إدارة العروض

    def load_promotions(self):
        """تحميل العروض"""
        # بيانات تجريبية
        sample_promotions = [
            {
                'title': 'خصم نهاية الأسبوع',
                'type': 'خصم بالنسبة المئوية',
                'value': 20,
                'value_type': 'percentage',
                'start_date': datetime.now(),
                'end_date': datetime.now() + timedelta(days=3),
                'status': 'نشط'
            },
            {
                'title': 'عرض الكمية الخاص',
                'type': 'اشتر واحصل',
                'value': '2+1',
                'value_type': 'buy_get',
                'start_date': datetime.now() - timedelta(days=2),
                'end_date': datetime.now() + timedelta(days=5),
                'status': 'نشط'
            },
            {
                'title': 'خصم العضوية الذهبية',
                'type': 'خصم بالنسبة المئوية',
                'value': 15,
                'value_type': 'percentage',
                'start_date': datetime.now() - timedelta(days=10),
                'end_date': datetime.now() - timedelta(days=1),
                'status': 'منتهي'
            }
        ]

        self.promotions = sample_promotions
        self.display_promotions()

    def display_promotions(self):
        """عرض العروض في الشبكة"""
        # مسح العروض الحالية
        for i in reversed(range(self.promotions_layout.count())):
            child = self.promotions_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # عرض العروض الجديدة
        cols = 3  # عدد الأعمدة
        for index, promotion in enumerate(self.promotions):
            row = index // cols
            col = index % cols

            promotion_card = PromotionCard(promotion)
            self.promotions_layout.addWidget(promotion_card, row, col)

    def filter_promotions(self):
        """فلترة العروض"""
        # سيتم تنفيذها لاحقاً
        pass

    def on_promo_type_changed(self):
        """عند تغيير نوع العرض"""
        if self.percentage_radio.isChecked():
            self.promo_value_input.setRange(0, 100)
            self.promo_value_input.setSuffix("%")
        elif self.fixed_amount_radio.isChecked():
            self.promo_value_input.setRange(0, 999999)
            self.promo_value_input.setSuffix(" دج")
        else:  # buy_get
            self.promo_value_input.setRange(1, 10)
            self.promo_value_input.setSuffix(" قطعة")

        self.update_preview()

    def update_preview(self):
        """تحديث معاينة العرض"""
        # تحديث بيانات المعاينة
        preview_data = {
            'title': self.promo_name_input.text() or 'عرض جديد',
            'type': self.get_selected_promo_type(),
            'value': self.promo_value_input.value(),
            'value_type': self.get_value_type(),
            'start_date': self.start_date_input.date().toPyDate(),
            'end_date': self.end_date_input.date().toPyDate(),
            'status': 'نشط'
        }

        # إعادة إنشاء بطاقة المعاينة
        self.preview_card.setParent(None)
        self.preview_card = PromotionCard(preview_data)

        # إضافة البطاقة الجديدة
        preview_layout = self.preview_card.parent().layout()
        if preview_layout:
            preview_layout.insertWidget(1, self.preview_card, alignment=Qt.AlignCenter)

    def get_selected_promo_type(self):
        """الحصول على نوع العرض المحدد"""
        if self.percentage_radio.isChecked():
            return "خصم بالنسبة المئوية"
        elif self.fixed_amount_radio.isChecked():
            return "خصم بمبلغ ثابت"
        else:
            return "اشتر واحصل"

    def get_value_type(self):
        """الحصول على نوع القيمة"""
        if self.percentage_radio.isChecked():
            return "percentage"
        elif self.fixed_amount_radio.isChecked():
            return "fixed"
        else:
            return "buy_get"

    def save_promotion(self):
        """حفظ العرض"""
        if not self.promo_name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم العرض")
            return

        # هنا سيتم حفظ العرض في قاعدة البيانات
        QMessageBox.information(self, "تم الحفظ", "تم حفظ العرض بنجاح")
        self.clear_promotion_form()
        self.load_promotions()

    def clear_promotion_form(self):
        """مسح نموذج العرض"""
        self.promo_name_input.clear()
        self.promo_description.clear()
        self.percentage_radio.setChecked(True)
        self.promo_value_input.setValue(0)
        self.min_purchase_input.setValue(0)
        self.max_discount_input.setValue(0)
        self.start_date_input.setDate(QDate.currentDate())
        self.end_date_input.setDate(QDate.currentDate().addDays(7))
        self.update_preview()

    def use_template(self, template):
        """استخدام قالب"""
        self.promo_name_input.setText(template['name'])
        self.promo_description.setPlainText(template['description'])

        if template['type'] == 'percentage':
            self.percentage_radio.setChecked(True)
            self.promo_value_input.setValue(template['value'])
        elif template['type'] == 'buy_get':
            self.buy_get_radio.setChecked(True)

        self.update_preview()

        # الانتقال إلى تبويب العرض الجديد
        self.tab_widget.setCurrentIndex(1)
