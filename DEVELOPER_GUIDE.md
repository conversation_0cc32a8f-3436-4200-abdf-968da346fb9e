# 👨‍💻 دليل المطور - Developer Guide

## 🏗️ هيكل المشروع

### 📁 المجلدات الرئيسية

```
advanced-pos-system/
├── 📄 main.py                    # نقطة الدخول الرئيسية
├── 📄 run.py                     # ملف التشغيل السريع
├── 📄 config.json                # ملف الإعدادات
├── 📄 requirements.txt           # المتطلبات
├── 📄 README.md                  # الدليل الرئيسي
├── 📄 QUICK_START.md            # دليل البدء السريع
├── 📄 DEVELOPER_GUIDE.md        # هذا الملف
├── 📄 test_interfaces.py        # اختبار الواجهات
│
├── 📂 database/                  # قاعدة البيانات
│   ├── database_manager.py      # مدير قاعدة البيانات
│   ├── models.py                # نماذج البيانات
│   └── pos_database.db          # ملف قاعدة البيانات
│
├── 📂 ui/                        # واجهات المستخدم
│   ├── main_window.py            # النافذة الرئيسية
│   ├── login_window.py           # نافذة تسجيل الدخول
│   ├── sales_tab.py              # تبويب المبيعات
│   ├── inventory_tab.py          # تبويب المخزون
│   ├── customers_tab.py          # تبويب العملاء
│   ├── reports_tab.py            # تبويب التقارير
│   ├── settings_tab.py           # تبويب الإعدادات
│   │
│   ├── 🆕 touchscreen_interface.py    # واجهة الشاشة اللمسية
│   ├── 🆕 cashier_interface.py        # واجهة الكاشير السريع
│   ├── 🆕 analytics_dashboard.py      # لوحة التحكم التحليلية
│   ├── 🆕 suppliers_management.py     # إدارة الموردين
│   ├── 🆕 promotions_manager.py       # مدير العروض والخصومات
│   └── 🆕 touch_payment_dialog.py     # نافذة الدفع اللمسية
│
├── 📂 utils/                     # أدوات مساعدة
│   ├── config_manager.py         # مدير الإعدادات
│   ├── logger.py                 # نظام السجلات
│   ├── barcode_generator.py      # مولد الباركود
│   ├── receipt_printer.py        # طابع الإيصالات
│   ├── 🆕 sound_manager.py            # مدير الأصوات
│   └── 🆕 payment_systems.py          # أنظمة الدفع الإلكتروني
│
├── 📂 reports/                   # التقارير
│   └── templates/                # قوالب التقارير
│
├── 📂 assets/                    # الموارد
│   ├── icons/                    # الأيقونات
│   ├── sounds/                   # الأصوات
│   └── fonts/                    # الخطوط
│
├── 📂 logs/                      # ملفات السجلات
├── 📂 backups/                   # النسخ الاحتياطية
└── 📂 docs/                      # الوثائق
```

## 🔧 الميزات الجديدة المضافة

### 1. 📱 واجهة الشاشة اللمسية
**الملف**: `ui/touchscreen_interface.py`

**المكونات الرئيسية**:
- `TouchButton` - زر محسن للمس
- `TouchNumPad` - لوحة أرقام للمس
- `TouchProductGrid` - شبكة المنتجات
- `TouchSalesInterface` - واجهة المبيعات اللمسية
- `TouchscreenInterface` - الواجهة الرئيسية

**الاستخدام**:
```python
from ui.touchscreen_interface import TouchscreenInterface

# إنشاء واجهة لمسية
touch_interface = TouchscreenInterface(db_manager, user_data)
```

### 2. ⚡ واجهة الكاشير السريع
**الملف**: `ui/cashier_interface.py`

**المكونات الرئيسية**:
- `QuickProductButton` - زر منتج سريع
- `QuickSaleItem` - عنصر بيع سريع
- `CashierInterface` - الواجهة الرئيسية

**المميزات**:
- أزرار منتجات سريعة
- واجهة مبسطة للعمليات السريعة
- اختصارات لوحة المفاتيح
- تحديث فوري للمجاميع

### 3. 📊 لوحة التحكم التحليلية
**الملف**: `ui/analytics_dashboard.py`

**المكونات**:
- `MetricCard` - بطاقة مقياس إحصائي
- `SimpleChart` - رسم بياني بسيط
- `AnalyticsDashboard` - لوحة التحكم الرئيسية

**أنواع الرسوم البيانية**:
- أعمدة (Bar Charts)
- خطوط (Line Charts)
- دوائر (Pie Charts)

### 4. 🏭 إدارة الموردين
**الملف**: `ui/suppliers_management.py`

**الميزات**:
- إضافة وتعديل الموردين
- إدارة طلبات الشراء
- تقارير الموردين
- تقييم الأداء

### 5. 🎯 مدير العروض والخصومات
**الملف**: `ui/promotions_manager.py`

**أنواع العروض**:
- خصم بالنسبة المئوية
- خصم بمبلغ ثابت
- اشتر واحصل على مجاني
- قوالب عروض جاهزة

### 6. 🔊 نظام الأصوات
**الملف**: `utils/sound_manager.py`

**المكونات**:
- `SoundManager` - مدير الأصوات
- `VoiceAnnouncer` - المعلن الصوتي
- `SoundNotificationManager` - مدير الإشعارات الصوتية

### 7. 💳 أنظمة الدفع الإلكتروني
**الملف**: `utils/payment_systems.py`

**مزودي الدفع**:
- `CashPaymentProvider` - الدفع النقدي
- `CardPaymentProvider` - الدفع بالبطاقة
- `BaridiMobProvider` - بريدي موب
- `CIBPaymentProvider` - CIB

## 🎨 نظام التصميم

### الألوان الرئيسية
```css
:root {
  --primary: #e0e5ec;      /* اللون الأساسي */
  --secondary: #34495e;    /* اللون الثانوي */
  --accent: #3498db;       /* لون التمييز */
  --success: #27ae60;      /* لون النجاح */
  --warning: #f39c12;      /* لون التحذير */
  --danger: #e74c3c;       /* لون الخطر */
}
```

### تأثيرات النيومورفيك
```css
/* تأثير بارز */
box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;

/* تأثير مضغوط */
box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
```

## 🔌 إضافة ميزات جديدة

### 1. إنشاء واجهة جديدة

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة جديدة - New Interface
وصف الواجهة
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class NewInterface(QWidget):
    """واجهة جديدة"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # العنوان
        title = QLabel("🆕 واجهة جديدة")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin: 20px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # المحتوى
        # أضف المحتوى هنا
```

### 2. إضافة التبويب للنافذة الرئيسية

في `ui/main_window.py`:

```python
# استيراد الواجهة الجديدة
from .new_interface import NewInterface

# في دالة setup_tabs()
def setup_tabs(self):
    # ... التبويبات الموجودة
    
    # التبويب الجديد
    self.new_tab = NewInterface(self.db_manager, self.user_data)
    self.tab_widget.addTab(self.new_tab, "🆕 جديد")
```

### 3. إضافة مزود دفع جديد

```python
class NewPaymentProvider(BasePaymentProvider):
    """مزود دفع جديد"""
    
    def __init__(self, config):
        super().__init__(config)
        self.api_key = config.get('api_key', '')
        
    def process_payment(self, amount, currency="DZD", customer_info=None):
        """معالجة الدفع"""
        # تنفيذ منطق الدفع
        return PaymentResult(
            success=True,
            transaction_id="NEW_12345",
            message="تم الدفع بنجاح",
            amount=amount,
            payment_method="مزود جديد"
        )
```

## 🧪 الاختبار

### تشغيل اختبار الواجهات
```bash
python test_interfaces.py
```

### اختبار مكون محدد
```python
# اختبار واجهة معينة
from ui.touchscreen_interface import TouchscreenInterface

def test_touchscreen():
    app = QApplication([])
    interface = TouchscreenInterface(db_manager, user_data)
    interface.show()
    app.exec_()
```

## 📝 إرشادات البرمجة

### 1. تسمية الملفات والفئات
- الملفات: `snake_case.py`
- الفئات: `PascalCase`
- الدوال: `snake_case`
- المتغيرات: `snake_case`

### 2. التعليقات والوثائق
```python
def function_name(param1, param2):
    """
    وصف الدالة باللغة العربية
    
    Args:
        param1: وصف المعامل الأول
        param2: وصف المعامل الثاني
        
    Returns:
        وصف القيمة المرجعة
    """
    pass
```

### 3. معالجة الأخطاء
```python
try:
    # الكود الرئيسي
    result = some_operation()
except SpecificException as e:
    # معالجة خطأ محدد
    logger.error(f"خطأ محدد: {e}")
except Exception as e:
    # معالجة الأخطاء العامة
    logger.error(f"خطأ عام: {e}")
finally:
    # تنظيف الموارد
    cleanup_resources()
```

## 🔄 التحديث والصيانة

### إضافة مكتبة جديدة
1. أضف المكتبة لـ `requirements.txt`
2. حدث `run.py` لفحص المكتبة
3. وثق الاستخدام في `README.md`

### تحديث قاعدة البيانات
1. أضف التغييرات في `database/models.py`
2. أنشئ دالة migration في `database_manager.py`
3. اختبر التحديث على بيانات تجريبية

### إضافة ترجمة جديدة
1. أنشئ ملف ترجمة في `assets/translations/`
2. حدث `config.json` لإضافة اللغة
3. طبق الترجمة في الواجهات

## 🐛 حل المشاكل الشائعة

### مشكلة الاستيراد
```python
# تأكد من المسار الصحيح
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
```

### مشكلة قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm database/pos_database.db
python main.py
```

### مشكلة الخطوط العربية
```python
# تطبيق اتجاه RTL
app.setLayoutDirection(Qt.RightToLeft)

# تعيين خط عربي
font = QFont("Arial", 12)
widget.setFont(font)
```

---

**تم إعداد هذا الدليل بواسطة**: Augment Agent  
**التاريخ**: 2025-06-15  
**الإصدار**: 1.0.0
