#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التشغيل السريع لنظام نقطة البيع
Quick Launch Script for POS System
"""

import sys
import os
import subprocess
import platform

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 جاري تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False

def check_requirements():
    """فحص المتطلبات"""
    required_packages = [
        'PyQt5',
        'Pillow',
        'reportlab',
        'matplotlib',
        'pandas',
        'openpyxl'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package.lower().replace('-', '_'))
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print(f"❌ المكتبات المفقودة: {', '.join(missing_packages)}")
        print("🔄 محاولة تثبيت المتطلبات...")
        return install_requirements()

    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        'database',
        'reports',
        'reports/templates',
        'assets',
        'assets/icons',
        'assets/sounds',
        'assets/fonts',
        'logs',
        'backups'
    ]

    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 تم إنشاء مجلد: {directory}")

def run_pos_system():
    """تشغيل نظام نقطة البيع"""
    print("🚀 جاري تشغيل نظام نقطة البيع...")

    # إضافة مسار المشروع
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

    try:
        # تشغيل النظام
        from main import main
        main()
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من وجود جميع الملفات المطلوبة")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🏪 نظام نقطة البيع المتقدم")
    print("Advanced POS System")
    print("=" * 50)

    # فحص إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return

    # إنشاء المجلدات
    create_directories()

    # فحص المتطلبات
    if not check_requirements():
        input("اضغط Enter للخروج...")
        return

    # تشغيل النظام
    run_pos_system()

if __name__ == "__main__":
    main()
