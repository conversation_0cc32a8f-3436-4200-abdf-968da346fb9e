#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة الموردين - Suppliers Management
نظام شامل لإدارة الموردين وطلبات الشراء
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget,
                            QTableWidgetItem, QFrame, QComboBox, QSpinBox,
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QDoubleSpinBox, QTextEdit, QGroupBox, QTabWidget,
                            QDateEdit, QCheckBox, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont

class SuppliersManagement(QWidget):
    """إدارة الموردين الرئيسية"""

    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.suppliers = []
        self.purchase_orders = []
        self.setup_ui()
        self.load_suppliers()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # عنوان الصفحة
        title = QLabel("🏭 إدارة الموردين وطلبات الشراء")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)

        # التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #e0e5ec;
                border-radius: 10px;
            }
            QTabBar::tab {
                background-color: #e0e5ec;
                color: #2c3e50;
                padding: 12px 20px;
                margin: 2px;
                border-radius: 8px;
                font-weight: bold;
                box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)

        # تبويب الموردين
        suppliers_tab = self.create_suppliers_tab()
        self.tab_widget.addTab(suppliers_tab, "🏭 الموردين")

        # تبويب طلبات الشراء
        purchase_orders_tab = self.create_purchase_orders_tab()
        self.tab_widget.addTab(purchase_orders_tab, "📋 طلبات الشراء")

        # تبويب تقارير الموردين
        reports_tab = self.create_suppliers_reports_tab()
        self.tab_widget.addTab(reports_tab, "📊 تقارير الموردين")

        main_layout.addWidget(self.tab_widget)

    def create_suppliers_tab(self):
        """إنشاء تبويب الموردين"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setSpacing(15)

        # الجانب الأيسر - نموذج إضافة مورد
        left_panel = self.create_add_supplier_panel()
        layout.addWidget(left_panel, 1)

        # الجانب الأيمن - قائمة الموردين
        right_panel = self.create_suppliers_list_panel()
        layout.addWidget(right_panel, 2)

        return widget

    def create_add_supplier_panel(self):
        """إنشاء لوحة إضافة مورد"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)

        layout = QVBoxLayout(panel)
        layout.setSpacing(15)

        # عنوان
        title = QLabel("➕ إضافة مورد جديد")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # نموذج البيانات
        form_group = QGroupBox("بيانات المورد")
        form_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)

        form_layout = QGridLayout(form_group)

        # اسم المورد
        form_layout.addWidget(QLabel("اسم المورد: *"), 0, 0)
        self.supplier_name_input = QLineEdit()
        self.supplier_name_input.setPlaceholderText("أدخل اسم المورد")
        form_layout.addWidget(self.supplier_name_input, 0, 1)

        # اسم جهة الاتصال
        form_layout.addWidget(QLabel("جهة الاتصال:"), 1, 0)
        self.contact_name_input = QLineEdit()
        self.contact_name_input.setPlaceholderText("اسم الشخص المسؤول")
        form_layout.addWidget(self.contact_name_input, 1, 1)

        # رقم الهاتف
        form_layout.addWidget(QLabel("رقم الهاتف:"), 2, 0)
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("رقم الهاتف")
        form_layout.addWidget(self.phone_input, 2, 1)

        # البريد الإلكتروني
        form_layout.addWidget(QLabel("البريد الإلكتروني:"), 3, 0)
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("البريد الإلكتروني")
        form_layout.addWidget(self.email_input, 3, 1)

        # العنوان
        form_layout.addWidget(QLabel("العنوان:"), 4, 0)
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(80)
        self.address_input.setPlaceholderText("عنوان المورد")
        form_layout.addWidget(self.address_input, 4, 1)

        # فئة المنتجات
        form_layout.addWidget(QLabel("فئة المنتجات:"), 5, 0)
        self.category_input = QLineEdit()
        self.category_input.setPlaceholderText("نوع المنتجات التي يوردها")
        form_layout.addWidget(self.category_input, 5, 1)

        # شروط الدفع
        form_layout.addWidget(QLabel("شروط الدفع:"), 6, 0)
        self.payment_terms_combo = QComboBox()
        self.payment_terms_combo.addItems([
            "نقدي فوري", "30 يوم", "60 يوم", "90 يوم", "حسب الاتفاق"
        ])
        form_layout.addWidget(self.payment_terms_combo, 6, 1)

        # تقييم المورد
        form_layout.addWidget(QLabel("التقييم:"), 7, 0)
        self.rating_combo = QComboBox()
        self.rating_combo.addItems(["⭐", "⭐⭐", "⭐⭐⭐", "⭐⭐⭐⭐", "⭐⭐⭐⭐⭐"])
        form_layout.addWidget(self.rating_combo, 7, 1)

        # تطبيق التنسيق
        input_style = """
            QLineEdit, QTextEdit, QComboBox {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #2c3e50;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
            }
        """

        for i in range(8):
            widget_item = form_layout.itemAtPosition(i, 1)
            if widget_item:
                widget_item.widget().setStyleSheet(input_style)

        # تطبيق تنسيق التسميات
        label_style = "color: #2c3e50; font-weight: bold;"
        for i in range(8):
            label_item = form_layout.itemAtPosition(i, 0)
            if label_item:
                label_item.widget().setStyleSheet(label_style)

        layout.addWidget(form_group)

        # أزرار العمل
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("💾 حفظ المورد")
        save_button.clicked.connect(self.save_supplier)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        clear_button = QPushButton("🗑️ مسح")
        clear_button.clicked.connect(self.clear_supplier_form)
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(clear_button)

        layout.addLayout(buttons_layout)
        layout.addStretch()

        return panel

    def create_suppliers_list_panel(self):
        """إنشاء لوحة قائمة الموردين"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)

        layout = QVBoxLayout(panel)
        layout.setSpacing(15)

        # عنوان وشريط البحث
        header_layout = QHBoxLayout()

        title = QLabel("📋 قائمة الموردين")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50;")
        header_layout.addWidget(title)

        header_layout.addStretch()

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 البحث في الموردين...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #2c3e50;
                min-width: 200px;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
            }
        """)
        self.search_input.textChanged.connect(self.filter_suppliers)
        header_layout.addWidget(self.search_input)

        layout.addLayout(header_layout)

        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(8)
        self.suppliers_table.setHorizontalHeaderLabels([
            "اسم المورد", "جهة الاتصال", "الهاتف", "البريد الإلكتروني",
            "فئة المنتجات", "شروط الدفع", "التقييم", "الحالة"
        ])

        # تنسيق الجدول
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                background-color: #e0e5ec;
                border: none;
                border-radius: 10px;
                gridline-color: #bdc3c7;
                selection-background-color: #3498db;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
        """)

        # إعدادات الجدول
        header = self.suppliers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # اسم المورد

        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.doubleClicked.connect(self.edit_supplier)

        layout.addWidget(self.suppliers_table)

        # أزرار العمليات
        buttons_layout = QHBoxLayout()

        edit_button = QPushButton("✏️ تعديل")
        edit_button.clicked.connect(self.edit_selected_supplier)

        delete_button = QPushButton("🗑️ حذف")
        delete_button.clicked.connect(self.delete_selected_supplier)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        contact_button = QPushButton("📞 اتصال")
        contact_button.clicked.connect(self.contact_supplier)

        order_button = QPushButton("📋 طلب شراء")
        order_button.clicked.connect(self.create_purchase_order)
        order_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        buttons_layout.addWidget(edit_button)
        buttons_layout.addWidget(delete_button)
        buttons_layout.addWidget(contact_button)
        buttons_layout.addWidget(order_button)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        return panel

    def create_purchase_orders_tab(self):
        """إنشاء تبويب طلبات الشراء"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        new_order_btn = QPushButton("📋 طلب شراء جديد")
        new_order_btn.clicked.connect(self.new_purchase_order)
        new_order_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        toolbar_layout.addWidget(new_order_btn)
        toolbar_layout.addStretch()

        # فلتر الحالة
        status_label = QLabel("الحالة:")
        status_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
        toolbar_layout.addWidget(status_label)

        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الطلبات", "قيد الانتظار", "مؤكد", "مستلم", "ملغي"])
        toolbar_layout.addWidget(self.status_filter)

        layout.addLayout(toolbar_layout)

        # جدول طلبات الشراء
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(7)
        self.orders_table.setHorizontalHeaderLabels([
            "رقم الطلب", "المورد", "التاريخ", "المبلغ", "الحالة", "تاريخ التسليم", "ملاحظات"
        ])

        self.orders_table.setStyleSheet(self.suppliers_table.styleSheet())

        # إعدادات الجدول
        header = self.orders_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)

        self.orders_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.orders_table.setAlternatingRowColors(True)

        layout.addWidget(self.orders_table)

        return widget

    def create_suppliers_reports_tab(self):
        """إنشاء تبويب تقارير الموردين"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # عنوان
        title = QLabel("📊 تقارير الموردين")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # إحصائيات سريعة
        stats_layout = QGridLayout()

        # إجمالي الموردين
        total_suppliers_card = self.create_stat_card("👥", "إجمالي الموردين", "0", "#3498db")
        stats_layout.addWidget(total_suppliers_card, 0, 0)

        # الموردين النشطين
        active_suppliers_card = self.create_stat_card("✅", "الموردين النشطين", "0", "#27ae60")
        stats_layout.addWidget(active_suppliers_card, 0, 1)

        # طلبات الشراء الشهر
        monthly_orders_card = self.create_stat_card("📋", "طلبات هذا الشهر", "0", "#e67e22")
        stats_layout.addWidget(monthly_orders_card, 0, 2)

        # إجمالي المشتريات
        total_purchases_card = self.create_stat_card("💰", "إجمالي المشتريات", "0.00 دج", "#9b59b6")
        stats_layout.addWidget(total_purchases_card, 0, 3)

        layout.addLayout(stats_layout)

        # تقارير تفصيلية
        reports_frame = QFrame()
        reports_frame.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)

        reports_layout = QVBoxLayout(reports_frame)

        reports_title = QLabel("📈 التقارير التفصيلية")
        reports_title.setFont(QFont("Arial", 14, QFont.Bold))
        reports_title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        reports_layout.addWidget(reports_title)

        # أزرار التقارير
        reports_buttons_layout = QHBoxLayout()

        supplier_performance_btn = QPushButton("📊 أداء الموردين")
        supplier_performance_btn.clicked.connect(self.show_supplier_performance_report)

        purchase_analysis_btn = QPushButton("📈 تحليل المشتريات")
        purchase_analysis_btn.clicked.connect(self.show_purchase_analysis_report)

        payment_terms_btn = QPushButton("💳 تحليل شروط الدفع")
        payment_terms_btn.clicked.connect(self.show_payment_terms_report)

        reports_buttons_layout.addWidget(supplier_performance_btn)
        reports_buttons_layout.addWidget(purchase_analysis_btn)
        reports_buttons_layout.addWidget(payment_terms_btn)
        reports_buttons_layout.addStretch()

        reports_layout.addLayout(reports_buttons_layout)

        # منطقة عرض التقرير
        self.report_display = QTextEdit()
        self.report_display.setReadOnly(True)
        self.report_display.setPlaceholderText("اختر تقريراً لعرضه هنا...")
        self.report_display.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: none;
                border-radius: 10px;
                padding: 15px;
                font-size: 12px;
            }
        """)
        reports_layout.addWidget(self.report_display)

        layout.addWidget(reports_frame)

        return widget

    def create_stat_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFixedSize(200, 100)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 20px; color: white;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("color: white; font-size: 10px; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        layout.addWidget(title_label)

        return card

    # وظائف إدارة الموردين

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        # بيانات تجريبية
        sample_suppliers = [
            ["شركة الإلكترونيات المتقدمة", "أحمد محمد", "0123456789", "<EMAIL>", "إلكترونيات", "30 يوم", "⭐⭐⭐⭐", "نشط"],
            ["مؤسسة الأغذية الطازجة", "فاطمة علي", "0987654321", "<EMAIL>", "مواد غذائية", "نقدي فوري", "⭐⭐⭐⭐⭐", "نشط"],
            ["شركة الملابس العصرية", "محمد حسن", "0555123456", "<EMAIL>", "ملابس", "60 يوم", "⭐⭐⭐", "نشط"],
        ]

        self.suppliers_table.setRowCount(len(sample_suppliers))

        for row, supplier in enumerate(sample_suppliers):
            for col, value in enumerate(supplier):
                item = QTableWidgetItem(value)
                item.setTextAlignment(Qt.AlignCenter)
                self.suppliers_table.setItem(row, col, item)

    def save_supplier(self):
        """حفظ مورد جديد"""
        if not self.supplier_name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المورد")
            return

        # هنا سيتم حفظ البيانات في قاعدة البيانات
        QMessageBox.information(self, "تم الحفظ", "تم حفظ المورد بنجاح")
        self.clear_supplier_form()
        self.load_suppliers()

    def clear_supplier_form(self):
        """مسح نموذج المورد"""
        self.supplier_name_input.clear()
        self.contact_name_input.clear()
        self.phone_input.clear()
        self.email_input.clear()
        self.address_input.clear()
        self.category_input.clear()
        self.payment_terms_combo.setCurrentIndex(0)
        self.rating_combo.setCurrentIndex(0)
        self.supplier_name_input.setFocus()

    def filter_suppliers(self):
        """فلترة الموردين"""
        search_text = self.search_input.text().lower()

        for row in range(self.suppliers_table.rowCount()):
            show_row = True

            if search_text:
                # البحث في اسم المورد وجهة الاتصال
                supplier_name = self.suppliers_table.item(row, 0).text().lower()
                contact_name = self.suppliers_table.item(row, 1).text().lower()

                if search_text not in supplier_name and search_text not in contact_name:
                    show_row = False

            self.suppliers_table.setRowHidden(row, not show_row)

    def edit_supplier(self):
        """تعديل مورد"""
        QMessageBox.information(self, "التعديل", "ميزة التعديل قيد التطوير")

    def edit_selected_supplier(self):
        """تعديل المورد المحدد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للتعديل")
            return
        self.edit_supplier()

    def delete_selected_supplier(self):
        """حذف المورد المحدد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للحذف")
            return

        supplier_name = self.suppliers_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المورد: {supplier_name}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الحذف", "تم حذف المورد بنجاح")
            self.load_suppliers()

    def contact_supplier(self):
        """الاتصال بالمورد"""
        QMessageBox.information(self, "الاتصال", "ميزة الاتصال قيد التطوير")

    def create_purchase_order(self):
        """إنشاء طلب شراء"""
        QMessageBox.information(self, "طلب الشراء", "ميزة طلب الشراء قيد التطوير")

    def new_purchase_order(self):
        """طلب شراء جديد"""
        QMessageBox.information(self, "طلب جديد", "ميزة الطلب الجديد قيد التطوير")

    def show_supplier_performance_report(self):
        """عرض تقرير أداء الموردين"""
        report_text = """
        📊 تقرير أداء الموردين
        ========================

        🏆 أفضل الموردين:
        1. مؤسسة الأغذية الطازجة - تقييم: ⭐⭐⭐⭐⭐
        2. شركة الإلكترونيات المتقدمة - تقييم: ⭐⭐⭐⭐
        3. شركة الملابس العصرية - تقييم: ⭐⭐⭐

        📈 إحصائيات الأداء:
        • متوسط وقت التسليم: 5 أيام
        • معدل الالتزام بالمواعيد: 85%
        • معدل جودة المنتجات: 92%

        💡 التوصيات:
        • زيادة التعامل مع الموردين ذوي التقييم العالي
        • مراجعة شروط التسليم مع الموردين المتأخرين
        • تقييم دوري لجودة المنتجات
        """

        self.report_display.setPlainText(report_text)

    def show_purchase_analysis_report(self):
        """عرض تقرير تحليل المشتريات"""
        report_text = """
        📈 تقرير تحليل المشتريات
        =========================

        💰 إجمالي المشتريات هذا الشهر: 150,000 دج
        📦 عدد طلبات الشراء: 25 طلب
        📊 متوسط قيمة الطلب: 6,000 دج

        🔝 أكثر الفئات شراءً:
        1. إلكترونيات: 45,000 دج (30%)
        2. مواد غذائية: 37,500 دج (25%)
        3. ملابس: 30,000 دج (20%)
        4. أدوات منزلية: 22,500 دج (15%)
        5. أخرى: 15,000 دج (10%)

        📅 توزيع المشتريات الشهرية:
        • يناير: 120,000 دج
        • فبراير: 135,000 دج
        • مارس: 150,000 دج (الشهر الحالي)

        📊 اتجاه النمو: +25% مقارنة بالشهر الماضي
        """

        self.report_display.setPlainText(report_text)

    def show_payment_terms_report(self):
        """عرض تقرير شروط الدفع"""
        report_text = """
        💳 تقرير تحليل شروط الدفع
        ==========================

        📊 توزيع شروط الدفع:
        • نقدي فوري: 40% من الموردين
        • 30 يوم: 35% من الموردين
        • 60 يوم: 20% من الموردين
        • 90 يوم: 5% من الموردين

        💰 تأثير على التدفق النقدي:
        • المدفوعات الفورية: 60,000 دج شهرياً
        • المدفوعات المؤجلة 30 يوم: 52,500 دج
        • المدفوعات المؤجلة 60 يوم: 30,000 دج
        • المدفوعات المؤجلة 90 يوم: 7,500 دج

        📈 التوصيات:
        • التفاوض على شروط دفع أفضل مع الموردين الجدد
        • الاستفادة من خصومات الدفع المبكر
        • مراجعة دورية لشروط الدفع الحالية

        ⚠️ تنبيهات:
        • 3 فواتير مستحقة الدفع خلال الأسبوع القادم
        • مورد واحد يتطلب مراجعة شروط الدفع
        """

        self.report_display.setPlainText(report_text)