#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام السجلات - Logger System
تسجيل جميع العمليات والأخطاء في النظام
"""

import logging
import os
from datetime import datetime
from typing import Optional

class Logger:
    """نظام تسجيل العمليات"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        self.setup_logger()
    
    def setup_logger(self):
        """إعداد نظام السجلات"""
        # إنشاء مجلد السجلات إذا لم يكن موجوداً
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
        
        # إعداد تنسيق السجل
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        date_format = '%Y-%m-%d %H:%M:%S'
        
        # إعداد ملف السجل اليومي
        today = datetime.now().strftime('%Y-%m-%d')
        log_file = os.path.join(self.log_dir, f'pos_{today}.log')
        
        # إعداد المسجل الرئيسي
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            datefmt=date_format,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('POS_System')
    
    def log_info(self, message: str, user_id: Optional[int] = None):
        """تسجيل معلومة"""
        if user_id:
            message = f"[المستخدم {user_id}] {message}"
        self.logger.info(message)
    
    def log_warning(self, message: str, user_id: Optional[int] = None):
        """تسجيل تحذير"""
        if user_id:
            message = f"[المستخدم {user_id}] {message}"
        self.logger.warning(message)
    
    def log_error(self, message: str, user_id: Optional[int] = None, exception: Optional[Exception] = None):
        """تسجيل خطأ"""
        if user_id:
            message = f"[المستخدم {user_id}] {message}"
        
        if exception:
            message = f"{message} - الاستثناء: {str(exception)}"
        
        self.logger.error(message)
    
    def log_sale(self, invoice_number: str, total_amount: float, user_id: int):
        """تسجيل عملية بيع"""
        message = f"عملية بيع - رقم الفاتورة: {invoice_number}, المبلغ: {total_amount} دج"
        self.log_info(message, user_id)
    
    def log_login(self, username: str, success: bool, ip_address: str = ""):
        """تسجيل محاولة تسجيل دخول"""
        status = "نجح" if success else "فشل"
        message = f"تسجيل دخول {status} - المستخدم: {username}"
        if ip_address:
            message += f" - IP: {ip_address}"
        
        if success:
            self.log_info(message)
        else:
            self.log_warning(message)
    
    def log_inventory_change(self, product_name: str, old_quantity: int, 
                           new_quantity: int, user_id: int):
        """تسجيل تغيير في المخزون"""
        message = f"تغيير مخزون - المنتج: {product_name}, من {old_quantity} إلى {new_quantity}"
        self.log_info(message, user_id)
    
    def log_system_event(self, event: str, details: str = ""):
        """تسجيل حدث نظام"""
        message = f"حدث نظام - {event}"
        if details:
            message += f" - التفاصيل: {details}"
        self.log_info(message)
    
    def get_recent_logs(self, lines: int = 100) -> list:
        """الحصول على آخر السجلات"""
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            log_file = os.path.join(self.log_dir, f'pos_{today}.log')
            
            if not os.path.exists(log_file):
                return []
            
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if len(all_lines) > lines else all_lines
                
        except Exception as e:
            self.log_error(f"خطأ في قراءة السجلات: {e}")
            return []
