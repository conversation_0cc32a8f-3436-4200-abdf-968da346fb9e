# دليل التثبيت - نظام نقطة البيع 🚀

## متطلبات النظام 📋

### المتطلبات الأساسية
- **نظام التشغيل:** Windows 10/11، Linux، أو macOS
- **Python:** الإصدار 3.9 أو أحدث
- **الذاكرة:** 4 جيجابايت RAM على الأقل
- **المساحة:** 500 ميجابايت مساحة فارغة

### المتطلبات الاختيارية
- قارئ باركود USB
- طابعة حرارية (58mm أو 80mm)
- اتصال بالإنترنت للتحديثات

## خطوات التثبيت 🔧

### 1. تثبيت Python
إذا لم يكن Python مثبتاً على نظامك:

#### Windows:
1. قم بتحميل Python من [python.org](https://python.org)
2. تأك<PERSON> من تحديد "Add Python to PATH" أثناء التثبيت
3. افتح Command Prompt واكتب: `python --version`

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

#### macOS:
```bash
# باستخدام Homebrew
brew install python3
```

### 2. تحميل المشروع
```bash
# إذا كان لديك Git
git clone https://github.com/your-repo/pos-system.git
cd pos-system

# أو قم بتحميل الملف المضغوط وفك ضغطه
```

### 3. إنشاء بيئة افتراضية (مستحسن)
```bash
# إنشاء بيئة افتراضية
python -m venv pos_env

# تفعيل البيئة الافتراضية
# Windows:
pos_env\Scripts\activate

# Linux/macOS:
source pos_env/bin/activate
```

### 4. تثبيت المتطلبات
```bash
# تثبيت جميع المتطلبات
pip install -r requirements.txt

# أو تثبيت المكتبات يدوياً
pip install PyQt5==5.15.10
pip install Pillow==10.0.0
pip install reportlab==4.0.4
pip install matplotlib==3.7.2
pip install pandas==2.0.3
pip install openpyxl==3.1.2
pip install python-bidi==0.4.2
pip install arabic-reshaper==3.0.0
pip install qrcode==7.4.2
pip install bcrypt==4.0.1
pip install cryptography==41.0.3
pip install requests==2.31.0
pip install python-dateutil==2.8.2
```

### 5. اختبار التثبيت
```bash
# تشغيل اختبار بسيط
python test_simple.py

# إذا نجح الاختبار، قم بتشغيل النظام
python main.py
```

## حل المشاكل الشائعة 🔧

### مشكلة: "PyQt5 not found"
```bash
# حل 1: إعادة تثبيت PyQt5
pip uninstall PyQt5
pip install PyQt5

# حل 2: استخدام conda (إذا كان متاحاً)
conda install pyqt

# حل 3: تثبيت من مصدر مختلف
pip install --index-url https://pypi.org/simple/ PyQt5
```

### مشكلة: "Permission denied"
```bash
# Windows: تشغيل Command Prompt كمدير
# Linux/macOS: استخدام sudo
sudo pip install -r requirements.txt
```

### مشكلة: "Module not found"
```bash
# تأكد من أنك في المجلد الصحيح
cd pos-system

# تأكد من تفعيل البيئة الافتراضية
# Windows:
pos_env\Scripts\activate
# Linux/macOS:
source pos_env/bin/activate
```

### مشكلة: خطأ في قاعدة البيانات
```bash
# احذف قاعدة البيانات القديمة وأعد إنشاءها
rm pos_database.db
python main.py
```

### مشكلة: الخطوط العربية لا تظهر بشكل صحيح
1. تأكد من تثبيت خطوط عربية على النظام
2. في Windows: انسخ خطوط عربية إلى مجلد Fonts
3. أعد تشغيل النظام

## التحقق من التثبيت ✅

### اختبار سريع
```python
# افتح Python واكتب:
import PyQt5
import sqlite3
print("✅ جميع المكتبات مثبتة بنجاح!")
```

### اختبار شامل
```bash
python test_simple.py
```

يجب أن ترى رسائل مثل:
```
✅ PyQt5 متاح
✅ SQLite متاح
✅ قاعدة البيانات تعمل بشكل صحيح
✅ نافذة تسجيل الدخول متاحة
✅ النافذة الرئيسية متاحة
🎉 اختبار النظام مكتمل!
```

## تشغيل النظام 🎯

### الطريقة الأولى (مستحسنة):
```bash
python main.py
```

### الطريقة الثانية:
```bash
python run.py
```

### بيانات تسجيل الدخول الافتراضية:
- **المدير:** admin / admin123
- **الكاشير:** cashier / cashier123

## إعداد الطابعة (اختياري) 🖨️

### طابعة حرارية USB:
1. قم بتوصيل الطابعة بالكمبيوتر
2. ثبت تعريف الطابعة من موقع الشركة المصنعة
3. اختبر الطباعة من إعدادات Windows
4. في النظام: اذهب إلى الإعدادات > إعدادات الطباعة

### طابعة شبكة:
1. تأكد من اتصال الطابعة بالشبكة
2. احصل على عنوان IP الخاص بالطابعة
3. أضف الطابعة في إعدادات النظام

## إعداد قارئ الباركود (اختياري) 📱

### قارئ USB:
1. قم بتوصيل القارئ بالكمبيوتر
2. تأكد من أن القارئ يعمل كـ "keyboard input"
3. اختبر القارئ في أي محرر نصوص
4. في النظام: ركز على حقل الباركود وامسح أي منتج

## النسخ الاحتياطي 💾

### نسخ احتياطي يدوي:
```bash
# انسخ ملف قاعدة البيانات
cp pos_database.db backup_$(date +%Y%m%d).db
```

### نسخ احتياطي تلقائي:
- سيتم إضافة هذه الميزة في التحديثات القادمة

## التحديثات 🔄

### تحديث النظام:
```bash
# احصل على آخر التحديثات
git pull origin main

# أعد تثبيت المتطلبات
pip install -r requirements.txt --upgrade
```

## الدعم الفني 💬

إذا واجهت أي مشاكل:

1. **تحقق من ملف README.md** للمعلومات الأساسية
2. **راجع هذا الدليل** للحلول الشائعة
3. **تشغيل الاختبار:** `python test_simple.py`
4. **تواصل معنا:** <EMAIL>

## ملاحظات مهمة ⚠️

- **احتفظ بنسخة احتياطية** من قاعدة البيانات دورياً
- **لا تحذف ملف pos_database.db** إلا إذا كنت تريد البدء من جديد
- **استخدم البيئة الافتراضية** لتجنب تضارب المكتبات
- **تأكد من صلاحيات الكتابة** في مجلد النظام

---

**تم إعداد هذا الدليل بواسطة Augment Agent - 2025**

🌟 **للمزيد من المساعدة، راجع ملف README.md** 🌟
