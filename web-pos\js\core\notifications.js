/**
 * نظام الإشعارات والتنبيهات
 * Notifications and Alerts System
 */

import { Utils } from './utils.js';

export class Notifications {
    constructor() {
        this.utils = new Utils();
        this.container = null;
        this.notifications = [];
        this.maxNotifications = 5;
        this.defaultDuration = 5000; // 5 ثوان
        
        this.init();
    }

    /**
     * تهيئة نظام الإشعارات
     */
    init() {
        // إنشاء حاوي الإشعارات إذا لم يكن موجوداً
        this.container = document.getElementById('toastContainer');
        
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'toastContainer';
            this.container.className = 'toast-container position-fixed top-0 end-0 p-3';
            this.container.style.zIndex = '9999';
            document.body.appendChild(this.container);
        }
    }

    /**
     * عرض إشعار نجاح
     */
    success(message, title = 'نجح', options = {}) {
        return this.show({
            type: 'success',
            title,
            message,
            icon: 'bi-check-circle-fill',
            ...options
        });
    }

    /**
     * عرض إشعار خطأ
     */
    error(message, title = 'خطأ', options = {}) {
        return this.show({
            type: 'error',
            title,
            message,
            icon: 'bi-x-circle-fill',
            duration: 8000, // مدة أطول للأخطاء
            ...options
        });
    }

    /**
     * عرض إشعار تحذير
     */
    warning(message, title = 'تحذير', options = {}) {
        return this.show({
            type: 'warning',
            title,
            message,
            icon: 'bi-exclamation-triangle-fill',
            ...options
        });
    }

    /**
     * عرض إشعار معلومات
     */
    info(message, title = 'معلومات', options = {}) {
        return this.show({
            type: 'info',
            title,
            message,
            icon: 'bi-info-circle-fill',
            ...options
        });
    }

    /**
     * عرض إشعار مخصص
     */
    show(options) {
        const config = {
            type: 'info',
            title: '',
            message: '',
            icon: 'bi-info-circle-fill',
            duration: this.defaultDuration,
            closable: true,
            sound: true,
            ...options
        };

        // إنشاء معرف فريد للإشعار
        const id = this.utils.generateId('notification_');
        
        // إنشاء عنصر الإشعار
        const toast = this.createToastElement(id, config);
        
        // إضافة الإشعار للحاوي
        this.container.appendChild(toast);
        
        // إضافة الإشعار للقائمة
        this.notifications.push({
            id,
            element: toast,
            config,
            createdAt: new Date()
        });

        // تشغيل الصوت إذا كان مفعلاً
        if (config.sound) {
            this.utils.playSound(config.type);
        }

        // عرض الإشعار مع تأثير
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // إخفاء الإشعار تلقائياً
        if (config.duration > 0) {
            setTimeout(() => {
                this.hide(id);
            }, config.duration);
        }

        // إزالة الإشعارات الزائدة
        this.cleanupOldNotifications();

        return id;
    }

    /**
     * إنشاء عنصر الإشعار
     */
    createToastElement(id, config) {
        const toast = document.createElement('div');
        toast.id = id;
        toast.className = `toast neumorphic-card notification-${config.type}`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        // تحديد ألوان الإشعار
        const colors = {
            success: { bg: 'rgba(39, 174, 96, 0.1)', border: '#27ae60', icon: '#27ae60' },
            error: { bg: 'rgba(231, 76, 60, 0.1)', border: '#e74c3c', icon: '#e74c3c' },
            warning: { bg: 'rgba(243, 156, 18, 0.1)', border: '#f39c12', icon: '#f39c12' },
            info: { bg: 'rgba(52, 152, 219, 0.1)', border: '#3498db', icon: '#3498db' }
        };

        const color = colors[config.type] || colors.info;

        toast.style.cssText = `
            background: ${color.bg};
            border-left: 4px solid ${color.border};
            margin-bottom: 1rem;
            min-width: 300px;
            max-width: 400px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        `;

        // محتوى الإشعار
        const header = document.createElement('div');
        header.className = 'toast-header';
        header.style.cssText = `
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem 0.5rem;
            background: transparent;
            border: none;
        `;

        const icon = document.createElement('i');
        icon.className = `bi ${config.icon}`;
        icon.style.cssText = `
            color: ${color.icon};
            font-size: 1.1rem;
            margin-left: 0.5rem;
        `;

        const title = document.createElement('strong');
        title.className = 'me-auto';
        title.textContent = config.title;
        title.style.cssText = `
            color: var(--neu-text);
            font-weight: 600;
            flex: 1;
        `;

        const time = document.createElement('small');
        time.className = 'text-muted';
        time.textContent = 'الآن';
        time.style.cssText = `
            color: var(--neu-text-light);
            font-size: 0.8rem;
            margin: 0 0.5rem;
        `;

        header.appendChild(icon);
        header.appendChild(title);
        header.appendChild(time);

        // زر الإغلاق
        if (config.closable) {
            const closeBtn = document.createElement('button');
            closeBtn.type = 'button';
            closeBtn.className = 'btn-close';
            closeBtn.setAttribute('aria-label', 'إغلاق');
            closeBtn.style.cssText = `
                background: none;
                border: none;
                font-size: 1.2rem;
                color: var(--neu-text-light);
                cursor: pointer;
                padding: 0;
                margin-right: 0.5rem;
            `;
            closeBtn.innerHTML = '<i class="bi bi-x"></i>';
            closeBtn.onclick = () => this.hide(id);
            
            header.appendChild(closeBtn);
        }

        // جسم الإشعار
        const body = document.createElement('div');
        body.className = 'toast-body';
        body.style.cssText = `
            padding: 0 1rem 0.75rem;
            color: var(--neu-text);
            line-height: 1.4;
        `;
        body.textContent = config.message;

        toast.appendChild(header);
        toast.appendChild(body);

        // إضافة تأثيرات التفاعل
        toast.addEventListener('mouseenter', () => {
            toast.style.transform = 'translateX(-5px) scale(1.02)';
        });

        toast.addEventListener('mouseleave', () => {
            toast.style.transform = 'translateX(0) scale(1)';
        });

        return toast;
    }

    /**
     * إخفاء إشعار
     */
    hide(id) {
        const notification = this.notifications.find(n => n.id === id);
        
        if (notification) {
            const toast = notification.element;
            
            // تأثير الإخفاء
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%) scale(0.8)';
            
            // إزالة العنصر بعد انتهاء التأثير
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
                
                // إزالة من القائمة
                this.notifications = this.notifications.filter(n => n.id !== id);
            }, 300);
        }
    }

    /**
     * إخفاء جميع الإشعارات
     */
    hideAll() {
        this.notifications.forEach(notification => {
            this.hide(notification.id);
        });
    }

    /**
     * تنظيف الإشعارات القديمة
     */
    cleanupOldNotifications() {
        if (this.notifications.length > this.maxNotifications) {
            const oldNotifications = this.notifications
                .slice(0, this.notifications.length - this.maxNotifications);
            
            oldNotifications.forEach(notification => {
                this.hide(notification.id);
            });
        }
    }

    /**
     * عرض نافذة تأكيد
     */
    confirm(message, title = 'تأكيد', options = {}) {
        return new Promise((resolve) => {
            const config = {
                confirmText: 'تأكيد',
                cancelText: 'إلغاء',
                type: 'warning',
                ...options
            };

            // إنشاء نافذة التأكيد
            const modal = this.createConfirmModal(message, title, config);
            document.body.appendChild(modal);

            // عرض النافذة
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            // معالجة الأحداث
            modal.querySelector('.btn-confirm').onclick = () => {
                bsModal.hide();
                resolve(true);
            };

            modal.querySelector('.btn-cancel').onclick = () => {
                bsModal.hide();
                resolve(false);
            };

            // تنظيف عند الإغلاق
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        });
    }

    /**
     * إنشاء نافذة تأكيد
     */
    createConfirmModal(message, title, config) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.tabIndex = -1;
        modal.setAttribute('aria-hidden', 'true');

        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            warning: '#f39c12',
            info: '#3498db'
        };

        const color = colors[config.type] || colors.info;

        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content neumorphic-modal">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-question-circle" style="color: ${color}; margin-left: 0.5rem;"></i>
                            ${title}
                        </h5>
                    </div>
                    <div class="modal-body">
                        <p style="margin: 0; line-height: 1.5;">${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary neumorphic-btn btn-cancel">
                            <i class="bi bi-x-circle"></i>
                            ${config.cancelText}
                        </button>
                        <button type="button" class="btn btn-primary neumorphic-btn btn-confirm">
                            <i class="bi bi-check-circle"></i>
                            ${config.confirmText}
                        </button>
                    </div>
                </div>
            </div>
        `;

        return modal;
    }

    /**
     * عرض نافذة إدخال
     */
    prompt(message, title = 'إدخال', defaultValue = '', options = {}) {
        return new Promise((resolve) => {
            const config = {
                confirmText: 'تأكيد',
                cancelText: 'إلغاء',
                inputType: 'text',
                placeholder: 'أدخل القيمة...',
                ...options
            };

            // إنشاء نافذة الإدخال
            const modal = this.createPromptModal(message, title, defaultValue, config);
            document.body.appendChild(modal);

            // عرض النافذة
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            const input = modal.querySelector('.prompt-input');
            
            // التركيز على حقل الإدخال
            modal.addEventListener('shown.bs.modal', () => {
                input.focus();
                input.select();
            });

            // معالجة الأحداث
            const handleConfirm = () => {
                const value = input.value.trim();
                bsModal.hide();
                resolve(value || null);
            };

            const handleCancel = () => {
                bsModal.hide();
                resolve(null);
            };

            modal.querySelector('.btn-confirm').onclick = handleConfirm;
            modal.querySelector('.btn-cancel').onclick = handleCancel;
            
            // Enter للتأكيد، Escape للإلغاء
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    handleConfirm();
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    handleCancel();
                }
            });

            // تنظيف عند الإغلاق
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        });
    }

    /**
     * إنشاء نافذة إدخال
     */
    createPromptModal(message, title, defaultValue, config) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.tabIndex = -1;
        modal.setAttribute('aria-hidden', 'true');

        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content neumorphic-modal">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-pencil-square" style="color: var(--neu-primary); margin-left: 0.5rem;"></i>
                            ${title}
                        </h5>
                    </div>
                    <div class="modal-body">
                        <p style="margin-bottom: 1rem; line-height: 1.5;">${message}</p>
                        <input type="${config.inputType}" 
                               class="form-control neumorphic-input prompt-input" 
                               placeholder="${config.placeholder}"
                               value="${defaultValue}">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary neumorphic-btn btn-cancel">
                            <i class="bi bi-x-circle"></i>
                            ${config.cancelText}
                        </button>
                        <button type="button" class="btn btn-primary neumorphic-btn btn-confirm">
                            <i class="bi bi-check-circle"></i>
                            ${config.confirmText}
                        </button>
                    </div>
                </div>
            </div>
        `;

        return modal;
    }

    /**
     * عرض شريط التقدم
     */
    showProgress(title = 'جاري المعالجة...', message = '') {
        const id = this.utils.generateId('progress_');
        
        const progressToast = document.createElement('div');
        progressToast.id = id;
        progressToast.className = 'toast neumorphic-card notification-info';
        progressToast.style.cssText = `
            background: rgba(52, 152, 219, 0.1);
            border-left: 4px solid #3498db;
            margin-bottom: 1rem;
            min-width: 350px;
            max-width: 450px;
        `;

        progressToast.innerHTML = `
            <div class="toast-header" style="background: transparent; border: none;">
                <i class="bi bi-hourglass-split" style="color: #3498db; margin-left: 0.5rem;"></i>
                <strong class="me-auto">${title}</strong>
            </div>
            <div class="toast-body">
                ${message ? `<p style="margin-bottom: 1rem;">${message}</p>` : ''}
                <div class="progress neumorphic-progress" style="height: 8px;">
                    <div class="progress-bar neumorphic-progress-bar" 
                         role="progressbar" 
                         style="width: 0%; transition: width 0.3s ease;"></div>
                </div>
                <div class="progress-text" style="text-align: center; margin-top: 0.5rem; font-size: 0.9rem; color: var(--neu-text-light);">
                    0%
                </div>
            </div>
        `;

        this.container.appendChild(progressToast);
        
        setTimeout(() => {
            progressToast.style.opacity = '1';
            progressToast.style.transform = 'translateX(0)';
        }, 100);

        return {
            id,
            update: (percent, text = '') => {
                const progressBar = progressToast.querySelector('.progress-bar');
                const progressText = progressToast.querySelector('.progress-text');
                
                if (progressBar) {
                    progressBar.style.width = `${Math.min(100, Math.max(0, percent))}%`;
                }
                
                if (progressText) {
                    progressText.textContent = text || `${Math.round(percent)}%`;
                }
            },
            close: () => {
                this.hide(id);
            }
        };
    }

    /**
     * الحصول على عدد الإشعارات النشطة
     */
    getActiveCount() {
        return this.notifications.length;
    }

    /**
     * تنظيف جميع الإشعارات
     */
    clear() {
        this.hideAll();
        this.notifications = [];
    }
}
