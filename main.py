#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام نقطة البيع (POS) - الملف الرئيسي
Point of Sale System - Main Entry Point

المطور: Augment Agent
التاريخ: 2025-06-15
الإصدار: 1.0.0
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QSplashScreen, QLabel
from PyQt5.QtCore import Qt, QTimer, QTranslator, QLocale
from PyQt5.QtGui import QPixmap, QFont, QFontDatabase

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.login_window import LoginWindow
from ui.main_window import MainWindow
from database.database_manager import DatabaseManager
from utils.config_manager import ConfigManager
from utils.logger import Logger

class POSApplication:
    """فئة التطبيق الرئيسية لنظام نقطة البيع"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.login_window = None
        self.db_manager = None
        self.config_manager = None
        self.logger = None
        
    def initialize_app(self):
        """تهيئة التطبيق الأساسية"""
        # إنشاء تطبيق Qt
        self.app = QApplication(sys.argv)
        
        # تعيين خصائص التطبيق
        self.app.setApplicationName("نظام نقطة البيع")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("شركة التقنية المتقدمة")
        
        # تحميل الخطوط العربية
        self.load_arabic_fonts()
        
        # تعيين اتجاه التطبيق من اليمين إلى اليسار
        self.app.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق الستايل النيومورفيك
        self.apply_neumorphic_style()
        
        return True
        
    def load_arabic_fonts(self):
        """تحميل الخطوط العربية"""
        try:
            # تحميل خط عربي افتراضي
            font_db = QFontDatabase()
            
            # البحث عن خطوط عربية متاحة في النظام
            families = font_db.families()
            arabic_fonts = [
                "Arial Unicode MS", "Tahoma", "Segoe UI", 
                "Microsoft Sans Serif", "Calibri"
            ]
            
            for font_name in arabic_fonts:
                if font_name in families:
                    font = QFont(font_name, 10)
                    font.setStyleHint(QFont.SansSerif)
                    self.app.setFont(font)
                    break
                    
        except Exception as e:
            print(f"خطأ في تحميل الخطوط: {e}")
    
    def apply_neumorphic_style(self):
        """تطبيق الستايل النيومورفيك"""
        neumorphic_style = """
        QMainWindow {
            background-color: #e0e5ec;
            color: #2c3e50;
        }
        
        QPushButton {
            background-color: #e0e5ec;
            border: none;
            border-radius: 15px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
        }
        
        QPushButton:hover {
            background-color: #d1d9e6;
        }
        
        QPushButton:pressed {
            background-color: #b8c6db;
            box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
        }
        
        QLineEdit, QTextEdit, QComboBox {
            background-color: #e0e5ec;
            border: none;
            border-radius: 10px;
            padding: 8px 12px;
            font-size: 12px;
            color: #2c3e50;
            box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
        }
        
        QTableWidget {
            background-color: #e0e5ec;
            border: none;
            border-radius: 10px;
            gridline-color: #bdc3c7;
            selection-background-color: #3498db;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #bdc3c7;
        }
        
        QHeaderView::section {
            background-color: #34495e;
            color: white;
            padding: 10px;
            border: none;
            font-weight: bold;
        }
        
        QFrame {
            background-color: #e0e5ec;
            border-radius: 15px;
            box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
        }
        
        QLabel {
            color: #2c3e50;
            font-weight: bold;
        }
        
        QMenuBar {
            background-color: #34495e;
            color: white;
            border: none;
            padding: 5px;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 8px 12px;
            border-radius: 5px;
        }
        
        QMenuBar::item:selected {
            background-color: #3498db;
        }
        
        QMenu {
            background-color: #e0e5ec;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            padding: 5px;
        }
        
        QMenu::item {
            padding: 8px 20px;
            border-radius: 5px;
        }
        
        QMenu::item:selected {
            background-color: #3498db;
            color: white;
        }
        """
        
        self.app.setStyleSheet(neumorphic_style)
    
    def initialize_components(self):
        """تهيئة مكونات النظام"""
        try:
            # تهيئة مدير التكوين
            self.config_manager = ConfigManager()
            
            # تهيئة نظام السجلات
            self.logger = Logger()
            
            # تهيئة قاعدة البيانات
            self.db_manager = DatabaseManager()
            if not self.db_manager.initialize_database():
                raise Exception("فشل في تهيئة قاعدة البيانات")
            
            self.logger.log_info("تم تهيئة جميع مكونات النظام بنجاح")
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة المكونات: {e}")
            return False
    
    def show_splash_screen(self):
        """عرض شاشة البداية"""
        splash_label = QLabel()
        splash_label.setText("""
        <div style='text-align: center; padding: 50px;'>
            <h1 style='color: #2c3e50; font-size: 24px; margin-bottom: 20px;'>
                🏪 نظام نقطة البيع المتقدم
            </h1>
            <p style='color: #7f8c8d; font-size: 14px;'>
                نظام شامل لإدارة المبيعات والمخزون
            </p>
            <p style='color: #95a5a6; font-size: 12px; margin-top: 30px;'>
                جاري التحميل...
            </p>
        </div>
        """)
        splash_label.setAlignment(Qt.AlignCenter)
        splash_label.setStyleSheet("""
            QLabel {
                background-color: #e0e5ec;
                border-radius: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        splash_label.resize(400, 300)
        
        splash = QSplashScreen(splash_label.grab())
        splash.show()
        
        # إظهار شاشة البداية لمدة 3 ثوان
        QTimer.singleShot(3000, splash.close)
        QTimer.singleShot(3000, self.show_login)
        
        return splash
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        self.login_window = LoginWindow(self.db_manager)
        self.login_window.login_successful.connect(self.show_main_window)
        self.login_window.show()
    
    def show_main_window(self, user_data):
        """عرض النافذة الرئيسية بعد تسجيل الدخول بنجاح"""
        if self.login_window:
            self.login_window.close()
        
        self.main_window = MainWindow(self.db_manager, user_data)
        self.main_window.show()
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            # تهيئة التطبيق
            if not self.initialize_app():
                return 1
            
            # تهيئة المكونات
            if not self.initialize_components():
                return 1
            
            # عرض شاشة البداية
            splash = self.show_splash_screen()
            
            # تشغيل حلقة الأحداث
            return self.app.exec_()
            
        except Exception as e:
            print(f"خطأ في تشغيل التطبيق: {e}")
            return 1

def main():
    """الدالة الرئيسية"""
    pos_app = POSApplication()
    sys.exit(pos_app.run())

if __name__ == "__main__":
    main()
