/**
 * تنسيقات العملاء
 * Customers Styles
 */

/* الحاوية الرئيسية */
.customers-container {
    padding: 1rem;
}

.customers-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 1.5rem;
}

.customers-header h2 {
    margin: 0;
    color: var(--neu-text);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.customers-actions {
    display: flex;
    gap: 1rem;
}

/* إحصائيات العملاء */
.customers-stats {
    margin-bottom: 2rem;
    padding: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--neu-bg-light);
    border-radius: var(--neu-radius);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--neu-shadow-hover);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--neu-primary), var(--neu-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.stat-info h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--neu-text);
}

.stat-info p {
    margin: 0;
    color: var(--neu-text-light);
    font-size: 0.9rem;
}

/* تصفية العملاء */
.customers-filters {
    margin-bottom: 2rem;
    padding: 1.5rem;
}

.filter-controls {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
    align-items: end;
}

/* جدول العملاء */
.customers-table-container {
    margin-bottom: 2rem;
    padding: 1.5rem;
    overflow: hidden;
}

.customer-name {
    font-weight: 600;
    color: var(--neu-text);
    margin-bottom: 0.25rem;
}

/* نافذة العميل */
.customer-details {
    padding: 1rem;
}

.customer-details .row {
    margin-bottom: 1.5rem;
}

.customer-details h6 {
    color: var(--neu-primary);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--neu-border);
}

.customer-details table {
    width: 100%;
    margin-bottom: 1rem;
}

.customer-details table td {
    padding: 0.5rem 0;
    border: none;
    vertical-align: top;
}

.customer-details table td:first-child {
    width: 40%;
    color: var(--neu-text-light);
}

.customer-details table td:last-child {
    color: var(--neu-text);
    font-weight: 500;
}

/* قائمة المستخدمين */
.users-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.user-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: var(--neu-bg-light);
    border-radius: var(--neu-radius);
    transition: all 0.3s ease;
}

.user-item:hover {
    background: var(--neu-bg-hover);
}

.user-info h6 {
    margin: 0 0 0.25rem 0;
    color: var(--neu-text);
    font-weight: 600;
}

.user-info p {
    margin: 0;
    color: var(--neu-text-light);
    font-size: 0.9rem;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-buttons .btn {
    padding: 0.5rem;
    min-width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

/* شارات الحالة */
.badge-primary {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

/* حالات فارغة */
.empty-customers {
    text-align: center;
    padding: 3rem;
    color: var(--neu-text-light);
}

.empty-customers i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-customers p {
    margin: 0;
    font-size: 1.2rem;
}

/* التجاوب */
@media (max-width: 1024px) {
    .filter-controls {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .customers-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .customers-actions {
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .customers-container {
        padding: 0.5rem;
    }
    
    .customers-header,
    .customers-stats,
    .customers-filters,
    .customers-table-container {
        padding: 1rem;
    }
    
    .stat-item {
        padding: 1rem;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .stat-info h3 {
        font-size: 1.2rem;
    }
    
    .user-item {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .user-actions {
        justify-content: center;
    }
}

/* تحسينات إضافية */
.table-borderless td {
    border: none !important;
}

.text-center {
    text-align: center;
}

.py-4 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mb-0 {
    margin-bottom: 0;
}

.d-flex {
    display: flex;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-center {
    align-items: center;
}

/* تأثيرات الانتقال */
.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات النوافذ المنبثقة */
.modal-lg {
    max-width: 800px;
}

.modal-dialog {
    margin: 1.75rem auto;
}

.modal-content {
    border: none;
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-modal);
}

.modal-header {
    background: var(--neu-bg-light);
    border-bottom: 1px solid var(--neu-border);
    border-radius: var(--neu-radius) var(--neu-radius) 0 0;
    padding: 1.5rem;
}

.modal-body {
    padding: 2rem;
    background: var(--neu-bg);
}

.modal-footer {
    background: var(--neu-bg-light);
    border-top: 1px solid var(--neu-border);
    border-radius: 0 0 var(--neu-radius) var(--neu-radius);
    padding: 1.5rem;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--neu-text-light);
    opacity: 0.7;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.btn-close:hover {
    opacity: 1;
}
