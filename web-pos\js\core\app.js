/**
 * نظام نقطة البيع المتقدم - التطبيق الرئيسي
 * Advanced POS System - Main Application
 */

import { Auth } from './auth.js';
import { Storage } from './storage.js';
import { Utils } from './utils.js';
import { Notifications } from './notifications.js';

// Import modules
import { Dashboard } from '../modules/dashboard.js';
import { Sales } from '../modules/sales.js';
import { Inventory } from '../modules/inventory.js';
import { Customers } from '../modules/customers.js';
import { Reports } from '../modules/reports.js';
import { Settings } from '../modules/settings.js';
import { Touchscreen } from '../modules/touchscreen.js';
import { QuickCashier } from '../modules/quickCashier.js';
import { Suppliers } from '../modules/suppliers.js';
import { Promotions } from '../modules/promotions.js';
import { Analytics } from '../modules/analytics.js';

export class App {
    constructor() {
        this.auth = new Auth();
        this.storage = new Storage();
        this.utils = new Utils();
        this.notifications = new Notifications();
        
        this.currentUser = null;
        this.currentTab = 'dashboard';
        this.modules = {};
        
        this.initializeModules();
        this.bindEvents();
    }

    /**
     * تهيئة التطبيق
     */
    async init() {
        try {
            // عرض شاشة التحميل
            this.showLoadingScreen();
            
            // تهيئة قاعدة البيانات المحلية
            await this.initializeDatabase();

            // تهيئة الوحدات
            this.initializeModules();

            // ربط الأحداث
            this.bindEvents();

            // فحص حالة تسجيل الدخول
            const savedUser = this.auth.getCurrentUser();
            if (savedUser && this.auth.isSessionValid()) {
                this.currentUser = savedUser;
                await this.showMainApp();
            } else {
                this.showLoginScreen();
            }

            // إخفاء شاشة التحميل
            this.hideLoadingScreen();
            
        } catch (error) {
            console.error('خطأ في تهيئة التطبيق:', error);
            this.notifications.error('حدث خطأ في تشغيل التطبيق');
            this.hideLoadingScreen();
            this.showLoginScreen();
        }
    }

    /**
     * تهيئة الوحدات
     */
    initializeModules() {
        this.modules = {
            dashboard: new Dashboard(this),
            sales: new Sales(this),
            inventory: new Inventory(this),
            customers: new Customers(this),
            reports: new Reports(this),
            settings: new Settings(this),
            touchscreen: new Touchscreen(this),
            quickCashier: new QuickCashier(this),
            suppliers: new Suppliers(this),
            promotions: new Promotions(this),
            analytics: new Analytics(this)
        };
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // تسجيل الدخول
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLogin.bind(this));
        }

        // قائمة الجانبية
        document.addEventListener('click', (e) => {
            if (e.target.closest('.menu-item')) {
                const menuItem = e.target.closest('.menu-item');
                const tab = menuItem.dataset.tab;
                const requiredRole = menuItem.dataset.role;
                
                if (requiredRole && !this.hasPermission(requiredRole)) {
                    this.notifications.warning('ليس لديك صلاحية للوصول لهذا القسم');
                    return;
                }
                
                this.switchTab(tab);
            }
        });

        // تسجيل الخروج
        window.logout = this.logout.bind(this);
        window.showProfile = this.showProfile.bind(this);
        window.showSettings = this.showSettings.bind(this);
        window.fillLogin = this.fillLogin.bind(this);

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));

        // حفظ البيانات عند إغلاق النافذة
        window.addEventListener('beforeunload', this.saveAppState.bind(this));

        // تحديث التاريخ والوقت
        this.updateDateTime();
        setInterval(this.updateDateTime.bind(this), 1000);
    }

    /**
     * تهيئة قاعدة البيانات المحلية
     */
    async initializeDatabase() {
        try {
            // إنشاء البيانات الافتراضية إذا لم تكن موجودة
            if (!this.storage.get('users')) {
                await this.createDefaultData();
            }
            
            // تحديث هيكل البيانات إذا لزم الأمر
            await this.updateDatabaseSchema();
            
        } catch (error) {
            console.error('خطأ في تهيئة قاعدة البيانات:', error);
            throw error;
        }
    }

    /**
     * إنشاء البيانات الافتراضية
     */
    async createDefaultData() {
        // المستخدمين الافتراضيين
        const defaultUsers = [
            {
                id: 1,
                username: 'admin',
                password: await this.utils.hashPassword('admin123'),
                fullName: 'المدير العام',
                role: 'admin',
                email: '<EMAIL>',
                phone: '0*********',
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null,
                permissions: {
                    sales: true,
                    inventory: true,
                    customers: true,
                    suppliers: true,
                    promotions: true,
                    reports: true,
                    analytics: true,
                    settings: true
                }
            },
            {
                id: 2,
                username: 'cashier',
                password: await this.utils.hashPassword('cashier123'),
                fullName: 'الكاشير',
                role: 'cashier',
                email: '<EMAIL>',
                phone: '0987654321',
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null,
                permissions: {
                    sales: true,
                    inventory: false,
                    customers: true,
                    suppliers: false,
                    promotions: false,
                    reports: false,
                    analytics: false,
                    settings: false
                }
            }
        ];

        // المنتجات التجريبية
        const defaultProducts = [
            {
                id: 1,
                name: 'لابتوب Dell',
                barcode: '*********0123',
                category: 'إلكترونيات',
                buyingPrice: 2500.00,
                sellingPrice: 3000.00,
                stock: 10,
                minStock: 2,
                unit: 'قطعة',
                description: 'لابتوب Dell Inspiron 15',
                supplier: 'شركة التقنية',
                isActive: true,
                createdAt: new Date().toISOString()
            },
            {
                id: 2,
                name: 'ماوس لاسلكي',
                barcode: '*********0124',
                category: 'إلكترونيات',
                buyingPrice: 50.00,
                sellingPrice: 75.00,
                stock: 25,
                minStock: 5,
                unit: 'قطعة',
                description: 'ماوس لاسلكي عالي الجودة',
                supplier: 'شركة التقنية',
                isActive: true,
                createdAt: new Date().toISOString()
            },
            {
                id: 3,
                name: 'كيبورد ميكانيكي',
                barcode: '*********0125',
                category: 'إلكترونيات',
                buyingPrice: 150.00,
                sellingPrice: 200.00,
                stock: 15,
                minStock: 3,
                unit: 'قطعة',
                description: 'كيبورد ميكانيكي للألعاب',
                supplier: 'شركة التقنية',
                isActive: true,
                createdAt: new Date().toISOString()
            }
        ];

        // العملاء التجريبيين
        const defaultCustomers = [
            {
                id: 1,
                name: 'أحمد محمد',
                phone: '0501234567',
                email: '<EMAIL>',
                address: 'الرياض، المملكة العربية السعودية',
                totalPurchases: 5000.00,
                lastPurchase: new Date().toISOString(),
                loyaltyPoints: 50,
                isActive: true,
                createdAt: new Date().toISOString()
            },
            {
                id: 2,
                name: 'فاطمة علي',
                phone: '0509876543',
                email: '<EMAIL>',
                address: 'جدة، المملكة العربية السعودية',
                totalPurchases: 3200.00,
                lastPurchase: new Date().toISOString(),
                loyaltyPoints: 32,
                isActive: true,
                createdAt: new Date().toISOString()
            }
        ];

        // الموردين التجريبيين
        const defaultSuppliers = [
            {
                id: 1,
                name: 'شركة التقنية المتقدمة',
                contactPerson: 'محمد أحمد',
                phone: '0112345678',
                email: '<EMAIL>',
                address: 'الرياض، المملكة العربية السعودية',
                category: 'إلكترونيات',
                paymentTerms: '30 يوم',
                rating: 5,
                isActive: true,
                createdAt: new Date().toISOString()
            }
        ];

        // إعدادات النظام
        const defaultSettings = {
            company: {
                name: 'شركة التقنية المتقدمة',
                address: 'الرياض، المملكة العربية السعودية',
                phone: '+966-11-123-4567',
                email: '<EMAIL>',
                taxNumber: '*********',
                commercialRegister: '1010123456',
                logo: null
            },
            pos: {
                currency: 'ريال سعودي',
                currencySymbol: 'ر.س',
                taxRate: 0.15,
                receiptFooter: 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',
                autoBackup: true,
                soundEnabled: true,
                printReceipt: true
            },
            display: {
                theme: 'neumorphic',
                language: 'ar',
                dateFormat: 'DD/MM/YYYY',
                timeFormat: '24h'
            }
        };

        // حفظ البيانات
        this.storage.set('users', defaultUsers);
        this.storage.set('products', defaultProducts);
        this.storage.set('customers', defaultCustomers);
        this.storage.set('suppliers', defaultSuppliers);
        this.storage.set('settings', defaultSettings);
        this.storage.set('sales', []);
        this.storage.set('promotions', []);
        this.storage.set('categories', ['إلكترونيات', 'ملابس', 'أغذية', 'أدوات منزلية']);
        
        console.log('تم إنشاء البيانات الافتراضية بنجاح');
    }

    /**
     * تحديث هيكل قاعدة البيانات
     */
    async updateDatabaseSchema() {
        const currentVersion = this.storage.get('dbVersion') || 1;
        const latestVersion = 1;

        if (currentVersion < latestVersion) {
            // تطبيق التحديثات حسب الإصدار
            console.log(`تحديث قاعدة البيانات من الإصدار ${currentVersion} إلى ${latestVersion}`);
            
            // حفظ الإصدار الجديد
            this.storage.set('dbVersion', latestVersion);
        }
    }

    /**
     * معالجة تسجيل الدخول
     */
    async handleLogin(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const loginBtn = document.querySelector('.login-btn');
        
        if (!username || !password) {
            this.notifications.error('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }

        // إظهار حالة التحميل
        loginBtn.classList.add('loading');
        loginBtn.innerHTML = '<span>جاري تسجيل الدخول...</span>';

        try {
            const user = await this.auth.login(username, password);
            
            if (user) {
                this.currentUser = user;
                this.notifications.success(`مرحباً ${user.fullName}`);
                await this.showMainApp();
            } else {
                this.notifications.error('اسم المستخدم أو كلمة المرور غير صحيحة');
                this.shakeLoginForm();
            }
            
        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            this.notifications.error('حدث خطأ في تسجيل الدخول');
            this.shakeLoginForm();
        } finally {
            // إخفاء حالة التحميل
            loginBtn.classList.remove('loading');
            loginBtn.innerHTML = '<i class="bi bi-box-arrow-in-right"></i> تسجيل الدخول';
        }
    }

    /**
     * عرض التطبيق الرئيسي
     */
    async showMainApp() {
        document.getElementById('loginScreen').classList.add('d-none');
        document.getElementById('mainApp').classList.remove('d-none');
        
        // تحديث معلومات المستخدم
        document.getElementById('currentUser').textContent = this.currentUser.fullName;
        
        // إخفاء العناصر حسب الصلاحيات
        this.updateUIPermissions();
        
        // تحميل لوحة التحكم
        await this.switchTab('dashboard');
        
        // تحديث الإحصائيات
        this.updateDashboardStats();
    }

    /**
     * عرض شاشة تسجيل الدخول
     */
    showLoginScreen() {
        document.getElementById('mainApp').classList.add('d-none');
        document.getElementById('loginScreen').classList.remove('d-none');
        
        // مسح الحقول
        document.getElementById('username').value = '';
        document.getElementById('password').value = '';
        document.getElementById('username').focus();
    }

    /**
     * تسجيل الخروج
     */
    logout() {
        this.auth.logout();
        this.currentUser = null;
        this.showLoginScreen();
        this.notifications.info('تم تسجيل الخروج بنجاح');
    }

    /**
     * التبديل بين التبويبات
     */
    async switchTab(tabName) {
        if (this.currentTab === tabName) return;

        // إخفاء التبويب الحالي
        const currentTabElement = document.getElementById(`${this.currentTab}-tab`);
        if (currentTabElement) {
            currentTabElement.classList.add('d-none');
        }

        // إزالة الحالة النشطة من القائمة
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });

        // تفعيل التبويب الجديد
        const newTabElement = document.getElementById(`${tabName}-tab`);
        const menuItem = document.querySelector(`[data-tab="${tabName}"]`);
        
        if (newTabElement && menuItem) {
            newTabElement.classList.remove('d-none');
            menuItem.classList.add('active');
            this.currentTab = tabName;

            // تحميل محتوى التبويب
            if (this.modules[tabName]) {
                await this.modules[tabName].render();
            }
        }
    }

    /**
     * فحص الصلاحيات
     */
    hasPermission(requiredRoles) {
        if (!this.currentUser) return false;
        
        const userRole = this.currentUser.role;
        const roles = requiredRoles.split(',');
        
        return roles.includes(userRole);
    }

    /**
     * تحديث واجهة المستخدم حسب الصلاحيات
     */
    updateUIPermissions() {
        document.querySelectorAll('[data-role]').forEach(element => {
            const requiredRoles = element.dataset.role;
            if (!this.hasPermission(requiredRoles)) {
                element.style.display = 'none';
            }
        });
    }

    /**
     * معالجة اختصارات لوحة المفاتيح
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + S: حفظ
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            this.saveAppState();
        }
        
        // F1: المساعدة
        if (e.key === 'F1') {
            e.preventDefault();
            this.showHelp();
        }
        
        // Escape: إغلاق النوافذ المنبثقة
        if (e.key === 'Escape') {
            this.closeModals();
        }
    }

    /**
     * عرض شاشة التحميل
     */
    showLoadingScreen() {
        document.getElementById('loadingScreen').classList.remove('d-none');
    }

    /**
     * إخفاء شاشة التحميل
     */
    hideLoadingScreen() {
        setTimeout(() => {
            document.getElementById('loadingScreen').classList.add('d-none');
        }, 1000);
    }

    /**
     * هز نموذج تسجيل الدخول عند الخطأ
     */
    shakeLoginForm() {
        const loginCard = document.querySelector('.login-card');
        loginCard.style.animation = 'shake 0.5s ease';
        setTimeout(() => {
            loginCard.style.animation = '';
        }, 500);
    }

    /**
     * ملء بيانات تسجيل الدخول (للحسابات التجريبية)
     */
    fillLogin(username, password) {
        document.getElementById('username').value = username;
        document.getElementById('password').value = password;
    }

    /**
     * تحديث التاريخ والوقت
     */
    updateDateTime() {
        const now = new Date();
        const dateElement = document.getElementById('currentDate');
        
        if (dateElement) {
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            
            dateElement.textContent = now.toLocaleDateString('ar-SA', options);
        }
    }

    /**
     * تحديث إحصائيات لوحة التحكم
     */
    updateDashboardStats() {
        if (this.modules.dashboard) {
            this.modules.dashboard.updateStats();
        }
    }

    /**
     * حفظ حالة التطبيق
     */
    saveAppState() {
        const appState = {
            currentTab: this.currentTab,
            lastSaved: new Date().toISOString()
        };
        
        this.storage.set('appState', appState);
    }

    /**
     * عرض الملف الشخصي
     */
    showProfile() {
        this.notifications.info('ميزة الملف الشخصي قيد التطوير');
    }

    /**
     * عرض الإعدادات
     */
    showSettings() {
        this.switchTab('settings');
    }

    /**
     * عرض المساعدة
     */
    showHelp() {
        this.notifications.info('ميزة المساعدة قيد التطوير');
    }

    /**
     * إغلاق النوافذ المنبثقة
     */
    closeModals() {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        });
    }

    /**
     * الحصول على المستخدم الحالي
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * الحصول على وحدة معينة
     */
    getModule(moduleName) {
        return this.modules[moduleName];
    }

    /**
     * إعادة تحميل البيانات
     */
    async reloadData() {
        try {
            // إعادة تحميل البيانات من التخزين المحلي
            await this.initializeDatabase();

            // تحديث الوحدات
            Object.values(this.modules).forEach(module => {
                if (module.reload) {
                    module.reload();
                }
            });

            // تحديث الإحصائيات
            this.updateDashboardStats();

            this.notifications.success('تم تحديث البيانات بنجاح');

        } catch (error) {
            console.error('خطأ في إعادة تحميل البيانات:', error);
            this.notifications.error('حدث خطأ في تحديث البيانات');
        }
    }

    /**
     * تصدير البيانات
     */
    exportData() {
        try {
            const data = {
                users: this.storage.get('users'),
                products: this.storage.get('products'),
                customers: this.storage.get('customers'),
                suppliers: this.storage.get('suppliers'),
                sales: this.storage.get('sales'),
                promotions: this.storage.get('promotions'),
                settings: this.storage.get('settings'),
                exportDate: new Date().toISOString()
            };

            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `pos-backup-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            this.notifications.success('تم تصدير البيانات بنجاح');

        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            this.notifications.error('حدث خطأ في تصدير البيانات');
        }
    }

    /**
     * استيراد البيانات
     */
    importData(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);

                    // التحقق من صحة البيانات
                    if (!data.users || !data.products || !data.settings) {
                        throw new Error('ملف البيانات غير صحيح');
                    }

                    // استيراد البيانات
                    Object.keys(data).forEach(key => {
                        if (key !== 'exportDate') {
                            this.storage.set(key, data[key]);
                        }
                    });

                    this.notifications.success('تم استيراد البيانات بنجاح');
                    this.reloadData();
                    resolve();

                } catch (error) {
                    console.error('خطأ في استيراد البيانات:', error);
                    this.notifications.error('حدث خطأ في استيراد البيانات');
                    reject(error);
                }
            };

            reader.onerror = () => {
                this.notifications.error('حدث خطأ في قراءة الملف');
                reject(new Error('خطأ في قراءة الملف'));
            };

            reader.readAsText(file);
        });
    }
}
