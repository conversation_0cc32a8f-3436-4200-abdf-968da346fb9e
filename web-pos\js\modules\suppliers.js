/**
 * وحدة إدارة الموردين
 * Suppliers Management Module
 */

export class Suppliers {
    constructor(app) {
        this.app = app;
        this.storage = app.storage;
        this.utils = app.utils;
        this.notifications = app.notifications;

        this.suppliers = [];
        this.currentSupplier = null;
    }

    /**
     * عرض واجهة الموردين
     */
    async render() {
        try {
            await this.loadData();
            this.createSuppliersInterface();
            this.bindEvents();
            this.loadSuppliersList();
        } catch (error) {
            console.error('خطأ في عرض واجهة الموردين:', error);
            this.notifications.error('حدث خطأ في تحميل واجهة الموردين');
        }
    }

    /**
     * تحميل البيانات
     */
    async loadData() {
        this.suppliers = this.storage.get('suppliers') || [];
    }

    /**
     * إنشاء واجهة الموردين
     */
    createSuppliersInterface() {
        const container = document.getElementById('suppliers-tab');
        if (!container) return;

        container.innerHTML = `
            <div class="suppliers-container">
                <div class="suppliers-header neumorphic-card">
                    <h2><i class="bi bi-truck"></i> إدارة الموردين</h2>
                    <div class="suppliers-actions">
                        <button class="btn neumorphic-btn btn-primary" onclick="suppliersModule.showAddSupplierModal()">
                            <i class="bi bi-plus-circle"></i>
                            إضافة مورد جديد
                        </button>
                        <button class="btn neumorphic-btn" onclick="suppliersModule.exportSuppliers()">
                            <i class="bi bi-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <div class="suppliers-stats neumorphic-card">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="bi bi-truck"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalSuppliers">0</h3>
                                <p>إجمالي الموردين</p>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="activeSuppliers">0</h3>
                                <p>الموردين النشطين</p>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="bi bi-star-fill"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="averageRating">0.0</h3>
                                <p>متوسط التقييم</p>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="bi bi-box"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalProducts">0</h3>
                                <p>المنتجات المورّدة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="suppliers-filters neumorphic-card">
                    <div class="filter-controls">
                        <div class="search-group">
                            <input type="text" id="supplierSearchInput" class="form-control neumorphic-input"
                                   placeholder="البحث بالاسم أو الهاتف...">
                        </div>
                        <div class="filter-group">
                            <select id="statusFilterSelect" class="form-select neumorphic-select">
                                <option value="">جميع الموردين</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <select id="categoryFilterSelect" class="form-select neumorphic-select">
                                <option value="">جميع الفئات</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="suppliers-table-container neumorphic-card">
                    <div class="table-responsive">
                        <table class="table neumorphic-table">
                            <thead>
                                <tr>
                                    <th>اسم المورد</th>
                                    <th>الشخص المسؤول</th>
                                    <th>الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الفئة</th>
                                    <th>التقييم</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="suppliersTableBody">
                                <!-- Suppliers will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Add/Edit Supplier Modal -->
            <div class="modal fade" id="supplierModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content neumorphic-modal">
                        <div class="modal-header">
                            <h5 class="modal-title" id="supplierModalTitle">
                                <i class="bi bi-plus-circle"></i>
                                إضافة مورد جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="supplierForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم المورد *</label>
                                            <input type="text" id="supplierName" class="form-control neumorphic-input" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الشخص المسؤول</label>
                                            <input type="text" id="contactPerson" class="form-control neumorphic-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف *</label>
                                            <input type="tel" id="supplierPhone" class="form-control neumorphic-input" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <input type="email" id="supplierEmail" class="form-control neumorphic-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea id="supplierAddress" class="form-control neumorphic-input" rows="3"></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">فئة المنتجات</label>
                                            <input type="text" id="supplierCategory" class="form-control neumorphic-input">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">شروط الدفع</label>
                                            <select id="paymentTerms" class="form-select neumorphic-select">
                                                <option value="نقدي">نقدي</option>
                                                <option value="15 يوم">15 يوم</option>
                                                <option value="30 يوم">30 يوم</option>
                                                <option value="60 يوم">60 يوم</option>
                                                <option value="90 يوم">90 يوم</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">التقييم</label>
                                            <select id="supplierRating" class="form-select neumorphic-select">
                                                <option value="5">ممتاز (5 نجوم)</option>
                                                <option value="4">جيد جداً (4 نجوم)</option>
                                                <option value="3">جيد (3 نجوم)</option>
                                                <option value="2">مقبول (2 نجمة)</option>
                                                <option value="1">ضعيف (1 نجمة)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحد الائتماني</label>
                                            <input type="number" id="creditLimit" class="form-control neumorphic-input"
                                                   step="0.01" min="0" value="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea id="supplierNotes" class="form-control neumorphic-input" rows="2"></textarea>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" id="supplierActive" class="form-check-input neumorphic-checkbox" checked>
                                    <label class="form-check-label" for="supplierActive">
                                        مورد نشط
                                    </label>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary neumorphic-btn" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle"></i>
                                إلغاء
                            </button>
                            <button type="button" class="btn btn-primary neumorphic-btn" onclick="suppliersModule.saveSupplier()">
                                <i class="bi bi-check-circle"></i>
                                حفظ
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // البحث
        const searchInput = document.getElementById('supplierSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', this.filterSuppliers.bind(this));
        }

        // تصفية الحالة
        const statusFilter = document.getElementById('statusFilterSelect');
        if (statusFilter) {
            statusFilter.addEventListener('change', this.filterSuppliers.bind(this));
        }

        // تصفية الفئة
        const categoryFilter = document.getElementById('categoryFilterSelect');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', this.filterSuppliers.bind(this));
        }

        // ربط الوحدة بالنافذة
        window.suppliersModule = this;
    }

    /**
     * تحميل قائمة الموردين
     */
    loadSuppliersList() {
        this.updateStats();
        this.loadCategories();
        this.filterSuppliers();
    }

    /**
     * تحديث الإحصائيات
     */
    updateStats() {
        const totalSuppliers = this.suppliers.length;
        const activeSuppliers = this.suppliers.filter(s => s.isActive).length;
        const averageRating = this.suppliers.length > 0 ?
            this.utils.average(this.suppliers, 'rating').toFixed(1) : 0;

        // حساب عدد المنتجات المورّدة
        const products = this.storage.get('products') || [];
        const suppliedProducts = products.filter(p =>
            this.suppliers.some(s => s.name === p.supplier)
        ).length;

        document.getElementById('totalSuppliers').textContent = totalSuppliers;
        document.getElementById('activeSuppliers').textContent = activeSuppliers;
        document.getElementById('averageRating').textContent = averageRating;
        document.getElementById('totalProducts').textContent = suppliedProducts;
    }

    /**
     * تحميل الفئات
     */
    loadCategories() {
        const categories = [...new Set(this.suppliers.map(s => s.category).filter(c => c))];
        const categoryFilter = document.getElementById('categoryFilterSelect');

        if (categoryFilter) {
            categoryFilter.innerHTML = '<option value="">جميع الفئات</option>' +
                categories.map(category =>
                    `<option value="${category}">${category}</option>`
                ).join('');
        }
    }

    /**
     * تصفية الموردين
     */
    filterSuppliers() {
        const searchTerm = document.getElementById('supplierSearchInput')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('statusFilterSelect')?.value || '';
        const categoryFilter = document.getElementById('categoryFilterSelect')?.value || '';

        let filteredSuppliers = this.suppliers;

        // تصفية بالبحث
        if (searchTerm) {
            filteredSuppliers = filteredSuppliers.filter(supplier =>
                this.utils.arabicSearch(supplier.name, searchTerm) ||
                supplier.phone.includes(searchTerm) ||
                (supplier.email && supplier.email.toLowerCase().includes(searchTerm)) ||
                (supplier.contactPerson && this.utils.arabicSearch(supplier.contactPerson, searchTerm))
            );
        }

        // تصفية بالحالة
        if (statusFilter) {
            filteredSuppliers = filteredSuppliers.filter(supplier => {
                if (statusFilter === 'active') return supplier.isActive;
                if (statusFilter === 'inactive') return !supplier.isActive;
                return true;
            });
        }

        // تصفية بالفئة
        if (categoryFilter) {
            filteredSuppliers = filteredSuppliers.filter(supplier =>
                supplier.category === categoryFilter
            );
        }

        this.displaySuppliers(filteredSuppliers);
    }

    /**
     * عرض الموردين في الجدول
     */
    displaySuppliers(suppliers) {
        const tbody = document.getElementById('suppliersTableBody');
        if (!tbody) return;

        if (suppliers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <i class="bi bi-truck" style="font-size: 2rem; color: var(--neu-text-light);"></i>
                        <p class="mt-2 mb-0">لا توجد موردين</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = suppliers.map(supplier => `
            <tr>
                <td>
                    <div class="supplier-name">${supplier.name}</div>
                    <small class="text-muted">ID: ${supplier.id}</small>
                </td>
                <td>${supplier.contactPerson || '-'}</td>
                <td>${this.utils.formatPhone(supplier.phone)}</td>
                <td>${supplier.email || '-'}</td>
                <td>
                    ${supplier.category ? `<span class="badge neumorphic-badge">${supplier.category}</span>` : '-'}
                </td>
                <td>
                    <div class="rating">
                        ${this.generateStars(supplier.rating || 0)}
                        <span class="rating-text">(${supplier.rating || 0})</span>
                    </div>
                </td>
                <td>
                    <span class="badge ${supplier.isActive ? 'neumorphic-badge badge-success' : 'neumorphic-badge badge-secondary'}">
                        ${supplier.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm neumorphic-btn" onclick="suppliersModule.viewSupplierDetails(${supplier.id})" title="عرض التفاصيل">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm neumorphic-btn" onclick="suppliersModule.editSupplier(${supplier.id})" title="تعديل">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm neumorphic-btn btn-danger" onclick="suppliersModule.deleteSupplier(${supplier.id})" title="حذف">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * توليد نجوم التقييم
     */
    generateStars(rating) {
        const fullStars = Math.floor(rating);
        const halfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);

        let stars = '';

        // نجوم ممتلئة
        for (let i = 0; i < fullStars; i++) {
            stars += '<i class="bi bi-star-fill text-warning"></i>';
        }

        // نجمة نصف ممتلئة
        if (halfStar) {
            stars += '<i class="bi bi-star-half text-warning"></i>';
        }

        // نجوم فارغة
        for (let i = 0; i < emptyStars; i++) {
            stars += '<i class="bi bi-star text-muted"></i>';
        }

        return stars;
    }

    /**
     * عرض نافذة إضافة مورد
     */
    showAddSupplierModal() {
        this.currentSupplier = null;
        document.getElementById('supplierModalTitle').innerHTML = '<i class="bi bi-plus-circle"></i> إضافة مورد جديد';
        this.resetSupplierForm();

        const modal = new bootstrap.Modal(document.getElementById('supplierModal'));
        modal.show();
    }

    /**
     * تعديل مورد
     */
    editSupplier(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        this.currentSupplier = supplier;
        document.getElementById('supplierModalTitle').innerHTML = '<i class="bi bi-pencil"></i> تعديل المورد';
        this.fillSupplierForm(supplier);

        const modal = new bootstrap.Modal(document.getElementById('supplierModal'));
        modal.show();
    }

    /**
     * ملء نموذج المورد
     */
    fillSupplierForm(supplier) {
        document.getElementById('supplierName').value = supplier.name;
        document.getElementById('contactPerson').value = supplier.contactPerson || '';
        document.getElementById('supplierPhone').value = supplier.phone;
        document.getElementById('supplierEmail').value = supplier.email || '';
        document.getElementById('supplierAddress').value = supplier.address || '';
        document.getElementById('supplierCategory').value = supplier.category || '';
        document.getElementById('paymentTerms').value = supplier.paymentTerms || 'نقدي';
        document.getElementById('supplierRating').value = supplier.rating || 5;
        document.getElementById('creditLimit').value = supplier.creditLimit || 0;
        document.getElementById('supplierNotes').value = supplier.notes || '';
        document.getElementById('supplierActive').checked = supplier.isActive;
    }

    /**
     * إعادة تعيين نموذج المورد
     */
    resetSupplierForm() {
        document.getElementById('supplierForm').reset();
        document.getElementById('supplierActive').checked = true;
        document.getElementById('supplierRating').value = 5;
        document.getElementById('paymentTerms').value = 'نقدي';
        document.getElementById('creditLimit').value = 0;
    }

    /**
     * حفظ المورد
     */
    async saveSupplier() {
        try {
            const formData = this.getSupplierFormData();

            // التحقق من صحة البيانات
            if (!this.validateSupplierData(formData)) {
                return;
            }

            if (this.currentSupplier) {
                // تحديث مورد موجود
                await this.updateSupplier(formData);
            } else {
                // إضافة مورد جديد
                await this.addSupplier(formData);
            }

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('supplierModal'));
            modal.hide();

            // تحديث القائمة
            this.loadSuppliersList();

        } catch (error) {
            console.error('خطأ في حفظ المورد:', error);
            this.notifications.error('حدث خطأ في حفظ المورد');
        }
    }

    /**
     * الحصول على بيانات النموذج
     */
    getSupplierFormData() {
        return {
            name: document.getElementById('supplierName').value.trim(),
            contactPerson: document.getElementById('contactPerson').value.trim(),
            phone: document.getElementById('supplierPhone').value.trim(),
            email: document.getElementById('supplierEmail').value.trim(),
            address: document.getElementById('supplierAddress').value.trim(),
            category: document.getElementById('supplierCategory').value.trim(),
            paymentTerms: document.getElementById('paymentTerms').value,
            rating: parseInt(document.getElementById('supplierRating').value),
            creditLimit: parseFloat(document.getElementById('creditLimit').value) || 0,
            notes: document.getElementById('supplierNotes').value.trim(),
            isActive: document.getElementById('supplierActive').checked
        };
    }

    /**
     * التحقق من صحة بيانات المورد
     */
    validateSupplierData(data) {
        if (!data.name) {
            this.notifications.error('اسم المورد مطلوب');
            return false;
        }

        if (!data.phone) {
            this.notifications.error('رقم الهاتف مطلوب');
            return false;
        }

        if (data.email && !this.utils.isValidEmail(data.email)) {
            this.notifications.error('البريد الإلكتروني غير صحيح');
            return false;
        }

        // فحص تكرار اسم المورد
        const existingSupplier = this.suppliers.find(s =>
            s.name === data.name &&
            (!this.currentSupplier || s.id !== this.currentSupplier.id)
        );

        if (existingSupplier) {
            this.notifications.error('اسم المورد موجود بالفعل');
            return false;
        }

        return true;
    }

    /**
     * إضافة مورد جديد
     */
    async addSupplier(data) {
        const newSupplier = {
            id: this.generateSupplierId(),
            ...data,
            totalOrders: 0,
            totalAmount: 0,
            lastOrder: null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.suppliers.push(newSupplier);
        this.storage.set('suppliers', this.suppliers);

        this.notifications.success('تم إضافة المورد بنجاح');
    }

    /**
     * تحديث مورد موجود
     */
    async updateSupplier(data) {
        const index = this.suppliers.findIndex(s => s.id === this.currentSupplier.id);
        if (index !== -1) {
            this.suppliers[index] = {
                ...this.currentSupplier,
                ...data,
                updatedAt: new Date().toISOString()
            };

            this.storage.set('suppliers', this.suppliers);
            this.notifications.success('تم تحديث المورد بنجاح');
        }
    }

    /**
     * حذف مورد
     */
    async deleteSupplier(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        const confirmed = await this.notifications.confirm(
            `هل تريد حذف المورد "${supplier.name}"؟`,
            'تأكيد الحذف'
        );

        if (confirmed) {
            const index = this.suppliers.findIndex(s => s.id === supplierId);
            if (index !== -1) {
                this.suppliers.splice(index, 1);
                this.storage.set('suppliers', this.suppliers);
                this.loadSuppliersList();
                this.notifications.success('تم حذف المورد بنجاح');
            }
        }
    }

    /**
     * عرض تفاصيل المورد
     */
    viewSupplierDetails(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        // يمكن إضافة نافذة منبثقة لعرض التفاصيل
        this.notifications.info(`تفاصيل المورد: ${supplier.name}`);
    }

    /**
     * توليد معرف مورد جديد
     */
    generateSupplierId() {
        const maxId = this.suppliers.reduce((max, supplier) => Math.max(max, supplier.id || 0), 0);
        return maxId + 1;
    }

    /**
     * تصدير الموردين
     */
    exportSuppliers() {
        try {
            const data = {
                suppliers: this.suppliers,
                exportDate: new Date().toISOString()
            };

            const dataStr = JSON.stringify(data, null, 2);
            this.utils.downloadFile(dataStr, `suppliers-${new Date().toISOString().split('T')[0]}.json`, 'application/json');

            this.notifications.success('تم تصدير الموردين بنجاح');
        } catch (error) {
            console.error('خطأ في تصدير الموردين:', error);
            this.notifications.error('حدث خطأ في تصدير الموردين');
        }
    }

    /**
     * إعادة تحميل البيانات
     */
    reload() {
        this.loadData();
        this.loadSuppliersList();
    }
}