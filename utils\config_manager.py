#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير التكوين - Configuration Manager
إدارة إعدادات النظام والتكوين
"""

import json
import os
from typing import Any, Dict, Optional

class ConfigManager:
    """مدير إعدادات النظام"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config_data = {}
        self.load_config()
    
    def load_config(self):
        """تحميل ملف التكوين"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
            else:
                # إنشاء ملف تكوين افتراضي
                self.config_data = self.get_default_config()
                self.save_config()
        except Exception as e:
            print(f"خطأ في تحميل ملف التكوين: {e}")
            self.config_data = self.get_default_config()
    
    def save_config(self):
        """حفظ ملف التكوين"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"خطأ في حفظ ملف التكوين: {e}")
    
    def get_default_config(self) -> Dict:
        """الحصول على التكوين الافتراضي"""
        return {
            "database": {
                "type": "sqlite",
                "path": "pos_database.db",
                "backup_interval": 24  # ساعات
            },
            "ui": {
                "theme": "neumorphic",
                "language": "ar",
                "font_size": 12,
                "window_size": {
                    "width": 1200,
                    "height": 800
                }
            },
            "printer": {
                "default_printer": "",
                "paper_width": 80,  # مم
                "auto_print": True,
                "copies": 1
            },
            "store": {
                "name": "متجر التقنية المتقدمة",
                "address": "الجزائر العاصمة",
                "phone": "+213-XXX-XXXXXX",
                "email": "<EMAIL>",
                "tax_number": "",
                "commercial_register": ""
            },
            "sales": {
                "tax_rate": 19.0,
                "currency": "دج",
                "decimal_places": 2,
                "auto_calculate_change": True,
                "require_customer_info": False
            },
            "inventory": {
                "low_stock_threshold": 5,
                "auto_reorder": False,
                "track_expiry_dates": False
            },
            "security": {
                "session_timeout": 30,  # دقائق
                "password_min_length": 6,
                "require_login": True,
                "audit_log": True
            },
            "backup": {
                "auto_backup": True,
                "backup_path": "backups/",
                "keep_backups": 30,  # أيام
                "cloud_backup": False
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """الحصول على قيمة إعداد"""
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """تعيين قيمة إعداد"""
        keys = key.split('.')
        config = self.config_data
        
        # التنقل إلى المستوى الأخير
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # تعيين القيمة
        config[keys[-1]] = value
        self.save_config()
    
    def get_store_info(self) -> Dict:
        """الحصول على معلومات المتجر"""
        return self.get('store', {})
    
    def get_printer_settings(self) -> Dict:
        """الحصول على إعدادات الطابعة"""
        return self.get('printer', {})
    
    def get_ui_settings(self) -> Dict:
        """الحصول على إعدادات الواجهة"""
        return self.get('ui', {})
    
    def update_store_info(self, store_data: Dict):
        """تحديث معلومات المتجر"""
        current_store = self.get('store', {})
        current_store.update(store_data)
        self.set('store', current_store)
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات إلى القيم الافتراضية"""
        self.config_data = self.get_default_config()
        self.save_config()
