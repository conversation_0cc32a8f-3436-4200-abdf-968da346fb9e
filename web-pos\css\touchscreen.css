/* ===== Touchscreen Interface Styles ===== */

.touchscreen-container {
    display: flex;
    height: calc(100vh - 80px);
    background: var(--neu-bg);
    overflow: hidden;
}

/* ===== Products Grid ===== */
.products-section {
    flex: 2;
    padding: 1rem;
    overflow-y: auto;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-outset);
}

.products-search {
    flex: 1;
    max-width: 400px;
    margin-left: 1rem;
}

.products-search input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--neu-radius);
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow-inset);
    font-size: 1rem;
    transition: var(--neu-transition);
}

.products-search input:focus {
    box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(52, 152, 219, 0.2);
    outline: none;
}

.category-filter {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    padding: 0.5rem;
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-outset);
    overflow-x: auto;
}

.category-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--neu-radius-sm);
    background: transparent;
    color: var(--neu-text);
    font-weight: 500;
    white-space: nowrap;
    transition: var(--neu-transition);
    cursor: pointer;
    min-width: 100px;
}

.category-btn:hover {
    background: rgba(52, 152, 219, 0.1);
    color: var(--neu-primary);
}

.category-btn.active {
    background: var(--neu-primary);
    color: white;
    box-shadow: var(--neu-shadow-outset);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    padding: 1rem 0;
}

.product-card {
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-outset);
    padding: 1.5rem;
    text-align: center;
    transition: var(--neu-transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--neu-shadow-hover);
}

.product-card:active {
    transform: translateY(0);
    box-shadow: var(--neu-shadow-pressed);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--neu-primary), var(--neu-info));
    border-radius: var(--neu-radius) var(--neu-radius) 0 0;
    opacity: 0;
    transition: var(--neu-transition);
}

.product-card:hover::before {
    opacity: 1;
}

.product-image {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, var(--neu-primary), var(--neu-info));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: var(--neu-shadow-outset);
}

.product-name {
    font-weight: 600;
    color: var(--neu-text);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
    line-height: 1.3;
}

.product-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--neu-primary);
    margin-bottom: 0.5rem;
}

.product-stock {
    font-size: 0.85rem;
    color: var(--neu-text-light);
}

.product-stock.low {
    color: var(--neu-warning);
    font-weight: 600;
}

.product-stock.out {
    color: var(--neu-danger);
    font-weight: 600;
}

/* ===== Cart Section ===== */
.cart-section {
    flex: 1;
    min-width: 400px;
    background: var(--neu-bg);
    border-right: 1px solid rgba(163, 177, 198, 0.2);
    display: flex;
    flex-direction: column;
}

.cart-header {
    padding: 1.5rem;
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow-outset);
    border-radius: 0 0 var(--neu-radius) var(--neu-radius);
}

.cart-header h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
    font-size: 1.25rem;
    color: var(--neu-text);
}

.cart-items {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-outset);
    transition: var(--neu-transition);
}

.cart-item:hover {
    box-shadow: var(--neu-shadow-hover);
}

.cart-item-image {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--neu-primary), var(--neu-info));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.cart-item-info {
    flex: 1;
    min-width: 0;
}

.cart-item-name {
    font-weight: 600;
    color: var(--neu-text);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.cart-item-price {
    color: var(--neu-text-light);
    font-size: 0.9rem;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

.quantity-btn {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 50%;
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow-outset);
    color: var(--neu-text);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--neu-transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-btn:hover {
    box-shadow: var(--neu-shadow-hover);
    color: var(--neu-primary);
}

.quantity-btn:active {
    box-shadow: var(--neu-shadow-pressed);
}

.quantity-display {
    min-width: 40px;
    text-align: center;
    font-weight: 600;
    color: var(--neu-text);
}

.remove-btn {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 50%;
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow-outset);
    color: var(--neu-danger);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--neu-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
}

.remove-btn:hover {
    box-shadow: var(--neu-shadow-hover);
    background: rgba(231, 76, 60, 0.1);
}

.remove-btn:active {
    box-shadow: var(--neu-shadow-pressed);
}

/* ===== Cart Summary ===== */
.cart-summary {
    padding: 1.5rem;
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow-outset);
    border-radius: var(--neu-radius) var(--neu-radius) 0 0;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding: 0.5rem 0;
}

.summary-row.total {
    border-top: 2px solid rgba(163, 177, 198, 0.3);
    padding-top: 1rem;
    margin-top: 1rem;
    font-size: 1.1rem;
    font-weight: 700;
}

.summary-label {
    color: var(--neu-text);
    font-weight: 500;
}

.summary-value {
    color: var(--neu-primary);
    font-weight: 600;
}

.summary-row.total .summary-value {
    font-size: 1.25rem;
    color: var(--neu-success);
}

/* ===== Action Buttons ===== */
.cart-actions {
    padding: 1rem 1.5rem;
    display: flex;
    gap: 1rem;
}

.action-btn {
    flex: 1;
    padding: 1rem;
    border: none;
    border-radius: var(--neu-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--neu-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.clear-btn {
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow-outset);
    color: var(--neu-text);
}

.clear-btn:hover {
    box-shadow: var(--neu-shadow-hover);
    color: var(--neu-danger);
}

.checkout-btn {
    background: linear-gradient(135deg, var(--neu-success), #229954);
    box-shadow: var(--neu-shadow-outset);
    color: white;
}

.checkout-btn:hover {
    box-shadow: var(--neu-shadow-hover);
    transform: translateY(-2px);
}

.checkout-btn:active {
    transform: translateY(0);
    box-shadow: var(--neu-shadow-pressed);
}

.checkout-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* ===== Empty Cart ===== */
.empty-cart {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--neu-text-light);
    text-align: center;
}

.empty-cart i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-cart p {
    font-size: 1.1rem;
    margin: 0;
}

/* ===== Touch Optimizations ===== */
@media (pointer: coarse) {
    .product-card {
        min-height: 200px;
        padding: 2rem 1.5rem;
    }
    
    .quantity-btn,
    .remove-btn {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
    
    .action-btn {
        padding: 1.25rem;
        font-size: 1.1rem;
    }
    
    .cart-item {
        padding: 1.25rem;
    }
}

/* ===== Responsive Design ===== */
@media (max-width: 1024px) {
    .touchscreen-container {
        flex-direction: column;
    }
    
    .products-section {
        flex: none;
        height: 60vh;
    }
    
    .cart-section {
        flex: none;
        min-width: auto;
        height: 40vh;
        border-right: none;
        border-top: 1px solid rgba(163, 177, 198, 0.2);
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .products-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .products-search {
        margin-right: 0;
        max-width: none;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.75rem;
    }
    
    .product-card {
        min-height: 150px;
        padding: 1rem;
    }
    
    .product-image {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .cart-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .touchscreen-container {
        height: calc(100vh - 60px);
    }
    
    .products-section,
    .cart-section {
        padding: 0.5rem;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.5rem;
    }
    
    .product-card {
        min-height: 120px;
        padding: 0.75rem;
    }
    
    .product-image {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .product-name {
        font-size: 0.85rem;
    }
    
    .product-price {
        font-size: 1rem;
    }
    
    .cart-item {
        padding: 1rem;
        gap: 0.75rem;
    }
    
    .cart-item-image {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}
