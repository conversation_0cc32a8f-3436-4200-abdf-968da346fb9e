#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة شاشة اللمس - Touchscreen Interface
واجهة محسنة لشاشات اللمس مع أزرار كبيرة وتفاعل سهل
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QPushButton, QFrame, QScrollArea,
                            QButtonGroup, QSizePolicy, QSpacerItem)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPalette, QColor, QPixmap, QIcon

class TouchButton(QPushButton):
    """زر محسن لشاشة اللمس"""
    
    def __init__(self, text="", icon_text="", parent=None):
        super().__init__(parent)
        self.setText(text)
        self.icon_text = icon_text
        self.setup_touch_button()
        
    def setup_touch_button(self):
        """إعداد الزر للمس"""
        # حجم كبير للمس السهل
        self.setMinimumSize(120, 80)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # خط كبير وواضح
        font = QFont("Arial", 12, QFont.Bold)
        self.setFont(font)
        
        # تنسيق نيومورفيك محسن للمس
        self.setStyleSheet("""
            QPushButton {
                background-color: #e0e5ec;
                border: none;
                border-radius: 20px;
                padding: 15px;
                color: #2c3e50;
                font-weight: bold;
                box-shadow: 8px 8px 16px #a3b1c6, -8px -8px 16px #ffffff;
            }
            QPushButton:hover {
                background-color: #d1d9e6;
                box-shadow: 6px 6px 12px #a3b1c6, -6px -6px 12px #ffffff;
            }
            QPushButton:pressed {
                background-color: #b8c6db;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
                transform: scale(0.98);
            }
            QPushButton:disabled {
                background-color: #95a5a6;
                color: #7f8c8d;
                box-shadow: none;
            }
        """)
        
        # تأثير الضغط
        self.pressed.connect(self.on_press_animation)
        
    def on_press_animation(self):
        """تأثير الضغط المرئي"""
        # يمكن إضافة تأثيرات إضافية هنا
        pass

class TouchNumPad(QWidget):
    """لوحة أرقام للمس"""
    
    number_entered = pyqtSignal(str)
    enter_pressed = pyqtSignal()
    clear_pressed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة لوحة الأرقام"""
        layout = QGridLayout(self)
        layout.setSpacing(10)
        
        # أزرار الأرقام
        self.number_buttons = {}
        numbers = [
            ['7', '8', '9'],
            ['4', '5', '6'],
            ['1', '2', '3'],
            ['0', '.', '⌫']
        ]
        
        for row, number_row in enumerate(numbers):
            for col, number in enumerate(number_row):
                btn = TouchButton(number)
                btn.setMinimumSize(80, 60)
                
                if number == '⌫':
                    btn.clicked.connect(self.clear_pressed.emit)
                    btn.setStyleSheet(btn.styleSheet() + """
                        QPushButton {
                            background-color: #e74c3c;
                            color: white;
                        }
                        QPushButton:hover {
                            background-color: #c0392b;
                        }
                    """)
                else:
                    btn.clicked.connect(lambda checked, n=number: self.number_entered.emit(n))
                
                layout.addWidget(btn, row, col)
                self.number_buttons[number] = btn
        
        # زر الإدخال
        enter_btn = TouchButton("إدخال", "✓")
        enter_btn.clicked.connect(self.enter_pressed.emit)
        enter_btn.setStyleSheet(enter_btn.styleSheet() + """
            QPushButton {
                background-color: #27ae60;
                color: white;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(enter_btn, 4, 0, 1, 3)

class TouchProductGrid(QWidget):
    """شبكة المنتجات للمس"""
    
    product_selected = pyqtSignal(dict)
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.products = []
        self.setup_ui()
        self.load_products()
        
    def setup_ui(self):
        """إعداد شبكة المنتجات"""
        layout = QVBoxLayout(self)
        
        # شريط البحث
        search_layout = QHBoxLayout()
        
        self.search_input = TouchButton("🔍 البحث عن منتج...")
        self.search_input.clicked.connect(self.show_search_keyboard)
        search_layout.addWidget(self.search_input)
        
        layout.addLayout(search_layout)
        
        # منطقة التمرير للمنتجات
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # إطار المنتجات
        self.products_frame = QWidget()
        self.products_layout = QGridLayout(self.products_frame)
        self.products_layout.setSpacing(15)
        
        scroll_area.setWidget(self.products_frame)
        layout.addWidget(scroll_area)
        
    def load_products(self):
        """تحميل المنتجات"""
        try:
            self.products = self.db_manager.get_all_products()
            self.display_products()
        except Exception as e:
            print(f"خطأ في تحميل المنتجات: {e}")
    
    def display_products(self):
        """عرض المنتجات في الشبكة"""
        # مسح المنتجات الحالية
        for i in reversed(range(self.products_layout.count())):
            self.products_layout.itemAt(i).widget().setParent(None)
        
        # عرض المنتجات الجديدة
        cols = 4  # عدد الأعمدة
        for index, product in enumerate(self.products):
            row = index // cols
            col = index % cols
            
            product_btn = self.create_product_button(product)
            self.products_layout.addWidget(product_btn, row, col)
    
    def create_product_button(self, product):
        """إنشاء زر منتج"""
        btn = TouchButton()
        btn.setMinimumSize(150, 120)
        btn.setMaximumSize(200, 150)
        
        # تنسيق المنتج
        product_text = f"{product['name']}\n{product['selling_price']:.2f} دج"
        if product['stock_quantity'] <= 0:
            product_text += "\n❌ نفد"
            btn.setEnabled(False)
        elif product['stock_quantity'] <= product.get('min_stock_level', 5):
            product_text += f"\n⚠️ {product['stock_quantity']}"
        else:
            product_text += f"\n✅ {product['stock_quantity']}"
        
        btn.setText(product_text)
        btn.clicked.connect(lambda: self.product_selected.emit(product))
        
        # تلوين حسب حالة المخزون
        if product['stock_quantity'] <= 0:
            btn.setStyleSheet(btn.styleSheet() + """
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                }
            """)
        elif product['stock_quantity'] <= product.get('min_stock_level', 5):
            btn.setStyleSheet(btn.styleSheet() + """
                QPushButton {
                    background-color: #f39c12;
                    color: white;
                }
            """)
        
        return btn
    
    def show_search_keyboard(self):
        """عرض لوحة المفاتيح للبحث"""
        # سيتم تنفيذها لاحقاً
        pass

class TouchSalesInterface(QWidget):
    """واجهة المبيعات المحسنة للمس"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.current_sale_items = []
        self.current_total = 0.0
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المبيعات"""
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(20)
        
        # الجانب الأيسر - شبكة المنتجات
        left_panel = self.create_products_panel()
        main_layout.addWidget(left_panel, 2)
        
        # الجانب الأوسط - الفاتورة
        middle_panel = self.create_invoice_panel()
        main_layout.addWidget(middle_panel, 2)
        
        # الجانب الأيمن - العمليات والدفع
        right_panel = self.create_actions_panel()
        main_layout.addWidget(right_panel, 1)
    
    def create_products_panel(self):
        """إنشاء لوحة المنتجات"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 20px;
                padding: 15px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        
        # عنوان
        title = QLabel("📦 المنتجات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # شبكة المنتجات
        self.product_grid = TouchProductGrid(self.db_manager)
        self.product_grid.product_selected.connect(self.add_product_to_sale)
        layout.addWidget(self.product_grid)
        
        return panel
    
    def create_invoice_panel(self):
        """إنشاء لوحة الفاتورة"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 20px;
                padding: 15px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        
        # عنوان الفاتورة
        title = QLabel("🧾 الفاتورة الحالية")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # منطقة عناصر الفاتورة
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        
        self.invoice_frame = QWidget()
        self.invoice_layout = QVBoxLayout(self.invoice_frame)
        
        scroll_area.setWidget(self.invoice_frame)
        layout.addWidget(scroll_area)
        
        # المجاميع
        totals_frame = self.create_totals_frame()
        layout.addWidget(totals_frame)
        
        return panel
    
    def create_totals_frame(self):
        """إنشاء إطار المجاميع"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 15px;
                padding: 20px;
                margin-top: 10px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # المجموع الفرعي
        self.subtotal_label = QLabel("المجموع الفرعي: 0.00 دج")
        self.subtotal_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        layout.addWidget(self.subtotal_label)
        
        # الضريبة
        self.tax_label = QLabel("الضريبة (19%): 0.00 دج")
        self.tax_label.setStyleSheet("color: #2ecc71; font-size: 16px; font-weight: bold;")
        layout.addWidget(self.tax_label)
        
        # المجموع النهائي
        self.total_label = QLabel("المجموع النهائي: 0.00 دج")
        self.total_label.setStyleSheet("color: #f39c12; font-size: 20px; font-weight: bold;")
        layout.addWidget(self.total_label)
        
        return frame
    
    def create_actions_panel(self):
        """إنشاء لوحة العمليات"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 20px;
                padding: 15px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # عنوان
        title = QLabel("⚡ العمليات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # أزرار الدفع
        self.cash_btn = TouchButton("💵\nدفع نقدي")
        self.cash_btn.setMinimumHeight(80)
        self.cash_btn.clicked.connect(self.process_cash_payment)
        self.cash_btn.setStyleSheet(self.cash_btn.styleSheet() + """
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(self.cash_btn)
        
        self.card_btn = TouchButton("💳\nدفع بالبطاقة")
        self.card_btn.setMinimumHeight(80)
        self.card_btn.clicked.connect(self.process_card_payment)
        self.card_btn.setStyleSheet(self.card_btn.styleSheet() + """
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(self.card_btn)
        
        # أزرار إضافية
        discount_btn = TouchButton("🏷️\nخصم")
        discount_btn.clicked.connect(self.apply_discount)
        layout.addWidget(discount_btn)
        
        clear_btn = TouchButton("🗑️\nمسح الكل")
        clear_btn.clicked.connect(self.clear_sale)
        clear_btn.setStyleSheet(clear_btn.styleSheet() + """
            QPushButton {
                background-color: #e74c3c;
                color: white;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        layout.addWidget(clear_btn)
        
        # لوحة الأرقام
        self.numpad = TouchNumPad()
        layout.addWidget(self.numpad)
        
        layout.addStretch()
        
        return panel
    
    def add_product_to_sale(self, product):
        """إضافة منتج للبيع"""
        # إنشاء عنصر فاتورة
        item_frame = QFrame()
        item_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
                box-shadow: 4px 4px 8px #a3b1c6;
            }
        """)
        
        item_layout = QHBoxLayout(item_frame)
        
        # معلومات المنتج
        product_info = QLabel(f"{product['name']}\n{product['selling_price']:.2f} دج")
        product_info.setStyleSheet("color: #2c3e50; font-weight: bold;")
        item_layout.addWidget(product_info, 2)
        
        # كمية
        qty_label = QLabel("الكمية: 1")
        qty_label.setStyleSheet("color: #7f8c8d;")
        item_layout.addWidget(qty_label, 1)
        
        # زر الحذف
        delete_btn = TouchButton("🗑️")
        delete_btn.setMaximumSize(50, 50)
        delete_btn.clicked.connect(lambda: self.remove_item(item_frame, product))
        delete_btn.setStyleSheet(delete_btn.styleSheet() + """
            QPushButton {
                background-color: #e74c3c;
                color: white;
            }
        """)
        item_layout.addWidget(delete_btn)
        
        self.invoice_layout.addWidget(item_frame)
        
        # إضافة للقائمة
        self.current_sale_items.append({
            'product_id': product['id'],
            'name': product['name'],
            'quantity': 1,
            'unit_price': product['selling_price'],
            'total_price': product['selling_price'],
            'frame': item_frame
        })
        
        self.update_totals()
    
    def remove_item(self, frame, product):
        """حذف عنصر من الفاتورة"""
        frame.setParent(None)
        self.current_sale_items = [
            item for item in self.current_sale_items 
            if item['frame'] != frame
        ]
        self.update_totals()
    
    def update_totals(self):
        """تحديث المجاميع"""
        subtotal = sum(item['total_price'] for item in self.current_sale_items)
        tax_amount = subtotal * 0.19
        total = subtotal + tax_amount
        
        self.subtotal_label.setText(f"المجموع الفرعي: {subtotal:.2f} دج")
        self.tax_label.setText(f"الضريبة (19%): {tax_amount:.2f} دج")
        self.total_label.setText(f"المجموع النهائي: {total:.2f} دج")
        
        self.current_total = total
        
        # تفعيل أزرار الدفع
        has_items = len(self.current_sale_items) > 0
        self.cash_btn.setEnabled(has_items)
        self.card_btn.setEnabled(has_items)
    
    def process_cash_payment(self):
        """معالجة الدفع النقدي"""
        from .touch_payment_dialog import TouchCashPaymentDialog
        dialog = TouchCashPaymentDialog(self.current_total, self)
        if dialog.exec_() == dialog.Accepted:
            self.complete_sale(dialog.get_payment_data())
    
    def process_card_payment(self):
        """معالجة الدفع بالبطاقة"""
        # سيتم تنفيذها لاحقاً
        pass
    
    def apply_discount(self):
        """تطبيق خصم"""
        # سيتم تنفيذها لاحقاً
        pass
    
    def clear_sale(self):
        """مسح البيع"""
        for item in self.current_sale_items:
            item['frame'].setParent(None)
        self.current_sale_items.clear()
        self.update_totals()
    
    def complete_sale(self, payment_data):
        """إتمام البيع"""
        # سيتم تنفيذها مع قاعدة البيانات
        self.clear_sale()
