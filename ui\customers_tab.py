#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تبويب العملاء - Customers Tab
واجهة إدارة العملاء ونقاط الولاء
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget, 
                            QTableWidgetItem, QFrame, QMessageBox, QHeaderView,
                            QAbstractItemView, QTextEdit, QGroupBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class CustomersTab(QWidget):
    """تبويب إدارة العملاء"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # الجانب الأيسر - نموذج إضافة عميل
        left_panel = self.create_add_customer_panel()
        main_layout.addWidget(left_panel, 1)
        
        # الجانب الأيمن - قائمة العملاء
        right_panel = self.create_customers_list_panel()
        main_layout.addWidget(right_panel, 2)
    
    def create_add_customer_panel(self):
        """إنشاء لوحة إضافة عميل"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 15px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # عنوان اللوحة
        title = QLabel("👥 إضافة عميل جديد")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # نموذج البيانات
        form_group = QGroupBox("بيانات العميل")
        form_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        form_layout = QGridLayout(form_group)
        
        # اسم العميل
        form_layout.addWidget(QLabel("الاسم: *"), 0, 0)
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسم العميل")
        form_layout.addWidget(self.name_input, 0, 1)
        
        # رقم الهاتف
        form_layout.addWidget(QLabel("الهاتف:"), 1, 0)
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("رقم الهاتف")
        form_layout.addWidget(self.phone_input, 1, 1)
        
        # البريد الإلكتروني
        form_layout.addWidget(QLabel("البريد الإلكتروني:"), 2, 0)
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("البريد الإلكتروني")
        form_layout.addWidget(self.email_input, 2, 1)
        
        # العنوان
        form_layout.addWidget(QLabel("العنوان:"), 3, 0)
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(80)
        self.address_input.setPlaceholderText("عنوان العميل")
        form_layout.addWidget(self.address_input, 3, 1)
        
        # تطبيق التنسيق
        input_style = """
            QLineEdit, QTextEdit {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #2c3e50;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
            }
        """
        
        for i in range(4):
            widget_item = form_layout.itemAtPosition(i, 1)
            if widget_item:
                widget_item.widget().setStyleSheet(input_style)
        
        # تطبيق تنسيق التسميات
        label_style = "color: #2c3e50; font-weight: bold;"
        for i in range(4):
            label_item = form_layout.itemAtPosition(i, 0)
            if label_item:
                label_item.widget().setStyleSheet(label_style)
        
        layout.addWidget(form_group)
        
        # أزرار العمل
        buttons_layout = QHBoxLayout()
        
        save_button = QPushButton("💾 حفظ العميل")
        save_button.clicked.connect(self.save_customer)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        clear_button = QPushButton("🗑️ مسح")
        clear_button.clicked.connect(self.clear_form)
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(clear_button)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        return panel
    
    def create_customers_list_panel(self):
        """إنشاء لوحة قائمة العملاء"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 15px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # عنوان اللوحة
        title = QLabel("📋 قائمة العملاء")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # شريط البحث
        search_layout = QHBoxLayout()
        
        search_label = QLabel("🔍 البحث:")
        search_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث بالاسم أو الهاتف...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #2c3e50;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
            }
        """)
        self.search_input.textChanged.connect(self.filter_customers)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        
        layout.addLayout(search_layout)
        
        # جدول العملاء
        self.customers_table = QTableWidget()
        self.customers_table.setColumnCount(6)
        self.customers_table.setHorizontalHeaderLabels([
            "الاسم", "الهاتف", "البريد الإلكتروني", "نقاط الولاء", 
            "إجمالي المشتريات", "تاريخ التسجيل"
        ])
        
        # تنسيق الجدول
        self.customers_table.setStyleSheet("""
            QTableWidget {
                background-color: #e0e5ec;
                border: none;
                border-radius: 10px;
                gridline-color: #bdc3c7;
                selection-background-color: #3498db;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        
        # إعدادات الجدول
        header = self.customers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # الاسم
        
        self.customers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.customers_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.customers_table)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        edit_button = QPushButton("✏️ تعديل")
        edit_button.clicked.connect(self.edit_customer)
        
        delete_button = QPushButton("🗑️ حذف")
        delete_button.clicked.connect(self.delete_customer)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        loyalty_button = QPushButton("⭐ نقاط الولاء")
        loyalty_button.clicked.connect(self.manage_loyalty)
        
        buttons_layout.addWidget(edit_button)
        buttons_layout.addWidget(delete_button)
        buttons_layout.addWidget(loyalty_button)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        return panel
    
    def save_customer(self):
        """حفظ العميل الجديد"""
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم العميل")
            return
        
        # سيتم تنفيذها لاحقاً مع قاعدة البيانات
        QMessageBox.information(self, "حفظ العميل", "تم حفظ العميل بنجاح")
        self.clear_form()
    
    def clear_form(self):
        """مسح النموذج"""
        self.name_input.clear()
        self.phone_input.clear()
        self.email_input.clear()
        self.address_input.clear()
        self.name_input.setFocus()
    
    def filter_customers(self):
        """فلترة العملاء"""
        # سيتم تنفيذها لاحقاً
        pass
    
    def edit_customer(self):
        """تعديل عميل"""
        QMessageBox.information(self, "التعديل", "ميزة التعديل قيد التطوير")
    
    def delete_customer(self):
        """حذف عميل"""
        QMessageBox.information(self, "الحذف", "ميزة الحذف قيد التطوير")
    
    def manage_loyalty(self):
        """إدارة نقاط الولاء"""
        QMessageBox.information(self, "نقاط الولاء", "ميزة نقاط الولاء قيد التطوير")
