#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة الدفع - Payment Dialog
نوافذ معالجة عمليات الدفع المختلفة
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QFrame, 
                            QDoubleSpinBox, QMessageBox, QTextEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class CashPaymentDialog(QDialog):
    """نافذة الدفع النقدي"""
    
    def __init__(self, total_amount, parent=None):
        super().__init__(parent)
        self.total_amount = total_amount
        self.payment_data = {}
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("الدفع النقدي")
        self.setFixedSize(400, 350)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title = QLabel("💵 الدفع النقدي")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # إطار المعلومات
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 10px;
                padding: 15px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        info_layout = QGridLayout(info_frame)
        
        # المبلغ المطلوب
        info_layout.addWidget(QLabel("المبلغ المطلوب:"), 0, 0)
        self.total_label = QLabel(f"{self.total_amount:.2f} دج")
        self.total_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 16px;")
        info_layout.addWidget(self.total_label, 0, 1)
        
        # المبلغ المدفوع
        info_layout.addWidget(QLabel("المبلغ المدفوع:"), 1, 0)
        self.paid_spinbox = QDoubleSpinBox()
        self.paid_spinbox.setRange(0, 999999.99)
        self.paid_spinbox.setDecimals(2)
        self.paid_spinbox.setValue(self.total_amount)
        self.paid_spinbox.setSuffix(" دج")
        self.paid_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
                color: #2c3e50;
                font-weight: bold;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
            }
        """)
        info_layout.addWidget(self.paid_spinbox, 1, 1)
        
        # الباقي
        info_layout.addWidget(QLabel("الباقي:"), 2, 0)
        self.change_label = QLabel("0.00 دج")
        self.change_label.setStyleSheet("color: #27ae60; font-weight: bold; font-size: 16px;")
        info_layout.addWidget(self.change_label, 2, 1)
        
        main_layout.addWidget(info_frame)
        
        # أزرار المبالغ السريعة
        quick_amounts_frame = QFrame()
        quick_layout = QVBoxLayout(quick_amounts_frame)
        
        quick_title = QLabel("مبالغ سريعة:")
        quick_title.setStyleSheet("color: #2c3e50; font-weight: bold;")
        quick_layout.addWidget(quick_title)
        
        # صف الأزرار الأول
        row1_layout = QHBoxLayout()
        amounts1 = [100, 200, 500, 1000]
        for amount in amounts1:
            btn = QPushButton(f"{amount} دج")
            btn.clicked.connect(lambda checked, a=amount: self.set_quick_amount(a))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 8px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            row1_layout.addWidget(btn)
        
        # صف الأزرار الثاني
        row2_layout = QHBoxLayout()
        amounts2 = [2000, 5000, 10000]
        for amount in amounts2:
            btn = QPushButton(f"{amount} دج")
            btn.clicked.connect(lambda checked, a=amount: self.set_quick_amount(a))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #9b59b6;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 8px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #8e44ad;
                }
            """)
            row2_layout.addWidget(btn)
        
        # زر المبلغ الدقيق
        exact_btn = QPushButton("المبلغ الدقيق")
        exact_btn.clicked.connect(self.set_exact_amount)
        exact_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        row2_layout.addWidget(exact_btn)
        
        quick_layout.addLayout(row1_layout)
        quick_layout.addLayout(row2_layout)
        
        main_layout.addWidget(quick_amounts_frame)
        
        # ملاحظات
        notes_label = QLabel("ملاحظات:")
        notes_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
        main_layout.addWidget(notes_label)
        
        self.notes_text = QTextEdit()
        self.notes_text.setMaximumHeight(60)
        self.notes_text.setPlaceholderText("ملاحظات إضافية (اختياري)")
        self.notes_text.setStyleSheet("""
            QTextEdit {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 8px;
                font-size: 12px;
                color: #2c3e50;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
            }
        """)
        main_layout.addWidget(self.notes_text)
        
        # أزرار العمل
        buttons_layout = QHBoxLayout()
        
        self.confirm_button = QPushButton("✅ تأكيد الدفع")
        self.confirm_button.clicked.connect(self.confirm_payment)
        self.confirm_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addWidget(self.confirm_button)
        buttons_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(buttons_layout)
        
        # تركيز على حقل المبلغ المدفوع
        self.paid_spinbox.setFocus()
        self.paid_spinbox.selectAll()
        
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.paid_spinbox.valueChanged.connect(self.calculate_change)
        
    def calculate_change(self):
        """حساب الباقي"""
        paid_amount = self.paid_spinbox.value()
        change = paid_amount - self.total_amount
        
        self.change_label.setText(f"{change:.2f} دج")
        
        # تغيير لون الباقي
        if change < 0:
            self.change_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 16px;")
            self.confirm_button.setEnabled(False)
        else:
            self.change_label.setStyleSheet("color: #27ae60; font-weight: bold; font-size: 16px;")
            self.confirm_button.setEnabled(True)
    
    def set_quick_amount(self, amount):
        """تعيين مبلغ سريع"""
        self.paid_spinbox.setValue(amount)
    
    def set_exact_amount(self):
        """تعيين المبلغ الدقيق"""
        self.paid_spinbox.setValue(self.total_amount)
    
    def confirm_payment(self):
        """تأكيد الدفع"""
        paid_amount = self.paid_spinbox.value()
        
        if paid_amount < self.total_amount:
            QMessageBox.warning(self, "مبلغ غير كافي", 
                              f"المبلغ المدفوع أقل من المطلوب\nالنقص: {self.total_amount - paid_amount:.2f} دج")
            return
        
        change = paid_amount - self.total_amount
        
        # تأكيد العملية
        message = f"المبلغ المطلوب: {self.total_amount:.2f} دج\n"
        message += f"المبلغ المدفوع: {paid_amount:.2f} دج\n"
        message += f"الباقي: {change:.2f} دج\n\n"
        message += "هل تريد تأكيد العملية؟"
        
        reply = QMessageBox.question(
            self, "تأكيد الدفع", message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.Yes:
            self.payment_data = {
                'method': 'نقدي',
                'amount': self.total_amount,
                'paid_amount': paid_amount,
                'change': change,
                'notes': self.notes_text.toPlainText().strip()
            }
            self.accept()
    
    def get_payment_data(self):
        """الحصول على بيانات الدفع"""
        return self.payment_data
