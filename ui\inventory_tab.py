#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تبويب المخزون - Inventory Tab
واجهة إدارة المخزون والمنتجات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget, 
                            QTableWidgetItem, QFrame, QComboBox, QSpinBox,
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QDoubleSpinBox, QTextEdit, QGroupBox, QFileDialog,
                            QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap

class InventoryTab(QWidget):
    """تبويب إدارة المخزون"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.setup_ui()
        self.load_products()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # التبويبات الفرعية
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #e0e5ec;
                border-radius: 10px;
            }
            QTabBar::tab {
                background-color: #e0e5ec;
                color: #2c3e50;
                padding: 10px 15px;
                margin: 2px;
                border-radius: 8px;
                font-weight: bold;
                box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        # تبويب عرض المنتجات
        self.products_tab = self.create_products_tab()
        self.tab_widget.addTab(self.products_tab, "📦 المنتجات")
        
        # تبويب إضافة منتج
        self.add_product_tab = self.create_add_product_tab()
        self.tab_widget.addTab(self.add_product_tab, "➕ إضافة منتج")
        
        # تبويب حركات المخزون
        self.movements_tab = self.create_movements_tab()
        self.tab_widget.addTab(self.movements_tab, "📊 حركات المخزون")
        
        main_layout.addWidget(self.tab_widget)
    
    def create_products_tab(self):
        """إنشاء تبويب عرض المنتجات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # شريط البحث والفلترة
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 10px;
                padding: 15px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        search_layout = QHBoxLayout(search_frame)
        
        # حقل البحث
        search_label = QLabel("🔍 البحث:")
        search_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث بالاسم أو الباركود...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #2c3e50;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
            }
        """)
        self.search_input.textChanged.connect(self.filter_products)
        
        # فلتر الفئة
        category_label = QLabel("الفئة:")
        category_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
        
        self.category_filter = QComboBox()
        self.category_filter.addItem("جميع الفئات", None)
        self.category_filter.setStyleSheet("""
            QComboBox {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #2c3e50;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
            }
        """)
        self.category_filter.currentTextChanged.connect(self.filter_products)
        
        # زر تحديث
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.clicked.connect(self.load_products)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input, 2)
        search_layout.addWidget(category_label)
        search_layout.addWidget(self.category_filter, 1)
        search_layout.addWidget(refresh_button)
        
        layout.addWidget(search_frame)
        
        # جدول المنتجات
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(8)
        self.products_table.setHorizontalHeaderLabels([
            "الباركود", "اسم المنتج", "الفئة", "سعر الشراء", 
            "سعر البيع", "المخزون", "الحد الأدنى", "الحالة"
        ])
        
        # تنسيق الجدول
        self.products_table.setStyleSheet("""
            QTableWidget {
                background-color: #e0e5ec;
                border: none;
                border-radius: 10px;
                gridline-color: #bdc3c7;
                selection-background-color: #3498db;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        
        # إعدادات الجدول
        header = self.products_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم المنتج
        
        self.products_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.products_table.setAlternatingRowColors(True)
        self.products_table.doubleClicked.connect(self.edit_product)
        
        layout.addWidget(self.products_table)
        
        # أزرار العمليات
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        
        edit_button = QPushButton("✏️ تعديل")
        edit_button.clicked.connect(self.edit_selected_product)
        
        delete_button = QPushButton("🗑️ حذف")
        delete_button.clicked.connect(self.delete_selected_product)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        stock_button = QPushButton("📊 تحديث المخزون")
        stock_button.clicked.connect(self.update_stock)
        
        export_button = QPushButton("📤 تصدير")
        export_button.clicked.connect(self.export_products)
        
        buttons_layout.addWidget(edit_button)
        buttons_layout.addWidget(delete_button)
        buttons_layout.addWidget(stock_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(export_button)
        
        layout.addWidget(buttons_frame)
        
        return widget
    
    def create_add_product_tab(self):
        """إنشاء تبويب إضافة منتج"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # إطار النموذج
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        form_layout = QGridLayout(form_frame)
        form_layout.setSpacing(15)
        
        # عنوان النموذج
        title = QLabel("➕ إضافة منتج جديد")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        form_layout.addWidget(title, 0, 0, 1, 2)
        
        # الباركود
        form_layout.addWidget(QLabel("الباركود:"), 1, 0)
        self.barcode_input = QLineEdit()
        self.barcode_input.setPlaceholderText("أدخل الباركود أو اتركه فارغاً للتوليد التلقائي")
        form_layout.addWidget(self.barcode_input, 1, 1)
        
        # اسم المنتج
        form_layout.addWidget(QLabel("اسم المنتج: *"), 2, 0)
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسم المنتج")
        form_layout.addWidget(self.name_input, 2, 1)
        
        # الوصف
        form_layout.addWidget(QLabel("الوصف:"), 3, 0)
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("وصف المنتج (اختياري)")
        form_layout.addWidget(self.description_input, 3, 1)
        
        # الفئة
        form_layout.addWidget(QLabel("الفئة:"), 4, 0)
        self.category_combo = QComboBox()
        self.load_categories()
        form_layout.addWidget(self.category_combo, 4, 1)
        
        # سعر الشراء
        form_layout.addWidget(QLabel("سعر الشراء:"), 5, 0)
        self.purchase_price_input = QDoubleSpinBox()
        self.purchase_price_input.setRange(0, 999999.99)
        self.purchase_price_input.setDecimals(2)
        self.purchase_price_input.setSuffix(" دج")
        form_layout.addWidget(self.purchase_price_input, 5, 1)
        
        # سعر البيع
        form_layout.addWidget(QLabel("سعر البيع: *"), 6, 0)
        self.selling_price_input = QDoubleSpinBox()
        self.selling_price_input.setRange(0, 999999.99)
        self.selling_price_input.setDecimals(2)
        self.selling_price_input.setSuffix(" دج")
        form_layout.addWidget(self.selling_price_input, 6, 1)
        
        # الكمية الأولية
        form_layout.addWidget(QLabel("الكمية الأولية:"), 7, 0)
        self.initial_stock_input = QSpinBox()
        self.initial_stock_input.setRange(0, 999999)
        form_layout.addWidget(self.initial_stock_input, 7, 1)
        
        # الحد الأدنى للمخزون
        form_layout.addWidget(QLabel("الحد الأدنى:"), 8, 0)
        self.min_stock_input = QSpinBox()
        self.min_stock_input.setRange(0, 999999)
        self.min_stock_input.setValue(5)
        form_layout.addWidget(self.min_stock_input, 8, 1)
        
        # الوحدة
        form_layout.addWidget(QLabel("الوحدة:"), 9, 0)
        self.unit_input = QLineEdit()
        self.unit_input.setText("قطعة")
        self.unit_input.setPlaceholderText("وحدة القياس")
        form_layout.addWidget(self.unit_input, 9, 1)
        
        # تطبيق التنسيق على جميع الحقول
        input_style = """
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #2c3e50;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
            }
        """
        
        for i in range(1, 10):
            widget_item = form_layout.itemAtPosition(i, 1)
            if widget_item:
                widget_item.widget().setStyleSheet(input_style)
        
        # تطبيق تنسيق التسميات
        label_style = "color: #2c3e50; font-weight: bold;"
        for i in range(1, 10):
            label_item = form_layout.itemAtPosition(i, 0)
            if label_item:
                label_item.widget().setStyleSheet(label_style)
        
        layout.addWidget(form_frame)
        
        # أزرار العمل
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("💾 حفظ المنتج")
        self.save_button.clicked.connect(self.save_product)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 25px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        clear_button = QPushButton("🗑️ مسح النموذج")
        clear_button.clicked.connect(self.clear_form)
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 25px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(clear_button)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        return widget
    
    def create_movements_tab(self):
        """إنشاء تبويب حركات المخزون"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # عنوان التبويب
        title = QLabel("📊 حركات المخزون")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # جدول الحركات
        self.movements_table = QTableWidget()
        self.movements_table.setColumnCount(6)
        self.movements_table.setHorizontalHeaderLabels([
            "التاريخ", "المنتج", "نوع الحركة", "الكمية", "المستخدم", "ملاحظات"
        ])
        
        self.movements_table.setStyleSheet(self.products_table.styleSheet())
        
        # إعدادات الجدول
        header = self.movements_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        layout.addWidget(self.movements_table)
        
        return widget

    def load_categories(self):
        """تحميل الفئات"""
        try:
            # سيتم تنفيذها عند إضافة جدول الفئات
            categories = [
                "مواد غذائية", "إلكترونيات", "ملابس",
                "مستحضرات تجميل", "أدوات منزلية"
            ]

            self.category_combo.clear()
            self.category_combo.addItem("اختر الفئة", None)

            for category in categories:
                self.category_combo.addItem(category, category)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الفئات: {e}")

    def load_products(self):
        """تحميل المنتجات"""
        try:
            products = self.db_manager.get_all_products()

            self.products_table.setRowCount(len(products))

            for row, product in enumerate(products):
                # الباركود
                barcode_item = QTableWidgetItem(product.get('barcode', ''))
                barcode_item.setData(Qt.UserRole, product['id'])
                self.products_table.setItem(row, 0, barcode_item)

                # اسم المنتج
                name_item = QTableWidgetItem(product['name'])
                self.products_table.setItem(row, 1, name_item)

                # الفئة
                category_item = QTableWidgetItem(product.get('category_name', 'غير محدد'))
                self.products_table.setItem(row, 2, category_item)

                # سعر الشراء
                purchase_price_item = QTableWidgetItem(f"{product.get('purchase_price', 0):.2f}")
                purchase_price_item.setTextAlignment(Qt.AlignCenter)
                self.products_table.setItem(row, 3, purchase_price_item)

                # سعر البيع
                selling_price_item = QTableWidgetItem(f"{product['selling_price']:.2f}")
                selling_price_item.setTextAlignment(Qt.AlignCenter)
                self.products_table.setItem(row, 4, selling_price_item)

                # المخزون
                stock_item = QTableWidgetItem(str(product['stock_quantity']))
                stock_item.setTextAlignment(Qt.AlignCenter)

                # تلوين المخزون حسب الكمية
                stock_qty = product['stock_quantity']
                min_level = product.get('min_stock_level', 5)

                if stock_qty <= 0:
                    stock_item.setBackground(Qt.red)
                    stock_item.setForeground(Qt.white)
                elif stock_qty <= min_level:
                    stock_item.setBackground(Qt.yellow)
                else:
                    stock_item.setBackground(Qt.green)
                    stock_item.setForeground(Qt.white)

                self.products_table.setItem(row, 5, stock_item)

                # الحد الأدنى
                min_stock_item = QTableWidgetItem(str(min_level))
                min_stock_item.setTextAlignment(Qt.AlignCenter)
                self.products_table.setItem(row, 6, min_stock_item)

                # الحالة
                status = "نشط" if product.get('is_active', True) else "غير نشط"
                status_item = QTableWidgetItem(status)
                status_item.setTextAlignment(Qt.AlignCenter)
                self.products_table.setItem(row, 7, status_item)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المنتجات: {e}")

    def filter_products(self):
        """فلترة المنتجات"""
        search_text = self.search_input.text().lower()

        for row in range(self.products_table.rowCount()):
            show_row = True

            if search_text:
                # البحث في الباركود واسم المنتج
                barcode = self.products_table.item(row, 0).text().lower()
                name = self.products_table.item(row, 1).text().lower()

                if search_text not in barcode and search_text not in name:
                    show_row = False

            self.products_table.setRowHidden(row, not show_row)

    def save_product(self):
        """حفظ المنتج الجديد"""
        try:
            # التحقق من البيانات المطلوبة
            if not self.name_input.text().strip():
                QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المنتج")
                return

            if self.selling_price_input.value() <= 0:
                QMessageBox.warning(self, "بيانات خاطئة", "يرجى إدخال سعر بيع صحيح")
                return

            # إعداد بيانات المنتج
            product_data = {
                'barcode': self.barcode_input.text().strip() or None,
                'name': self.name_input.text().strip(),
                'description': self.description_input.toPlainText().strip(),
                'category_id': self.category_combo.currentData(),
                'purchase_price': self.purchase_price_input.value(),
                'selling_price': self.selling_price_input.value(),
                'stock_quantity': self.initial_stock_input.value(),
                'min_stock_level': self.min_stock_input.value(),
                'unit': self.unit_input.text().strip() or 'قطعة'
            }

            # حفظ في قاعدة البيانات
            if self.db_manager.add_product(product_data):
                QMessageBox.information(self, "نجح الحفظ", "تم حفظ المنتج بنجاح")
                self.clear_form()
                self.load_products()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ المنتج")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ: {e}")

    def clear_form(self):
        """مسح النموذج"""
        self.barcode_input.clear()
        self.name_input.clear()
        self.description_input.clear()
        self.category_combo.setCurrentIndex(0)
        self.purchase_price_input.setValue(0)
        self.selling_price_input.setValue(0)
        self.initial_stock_input.setValue(0)
        self.min_stock_input.setValue(5)
        self.unit_input.setText("قطعة")
        self.name_input.setFocus()

    def edit_selected_product(self):
        """تعديل المنتج المحدد"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للتعديل")
            return

        self.edit_product()

    def edit_product(self):
        """تعديل منتج"""
        # سيتم تنفيذها لاحقاً
        QMessageBox.information(self, "التعديل", "ميزة التعديل قيد التطوير")

    def delete_selected_product(self):
        """حذف المنتج المحدد"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للحذف")
            return

        product_name = self.products_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المنتج: {product_name}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # سيتم تنفيذها لاحقاً
            QMessageBox.information(self, "الحذف", "ميزة الحذف قيد التطوير")

    def update_stock(self):
        """تحديث المخزون"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج لتحديث مخزونه")
            return

        # سيتم تنفيذها لاحقاً
        QMessageBox.information(self, "تحديث المخزون", "ميزة تحديث المخزون قيد التطوير")

    def export_products(self):
        """تصدير المنتجات"""
        # سيتم تنفيذها لاحقاً
        QMessageBox.information(self, "التصدير", "ميزة التصدير قيد التطوير")
