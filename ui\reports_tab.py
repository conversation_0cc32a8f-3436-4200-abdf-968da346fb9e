#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تبويب التقارير - Reports Tab
واجهة عرض التقارير والإحصائيات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QPushButton, QFrame, QComboBox, QDateEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QAbstractItemView, QGroupBox, QMessageBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

class ReportsTab(QWidget):
    """تبويب التقارير والإحصائيات"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان التبويب
        title = QLabel("📊 التقارير والإحصائيات")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # لوحة الفلاتر
        filters_panel = self.create_filters_panel()
        main_layout.addWidget(filters_panel)
        
        # لوحة الإحصائيات السريعة
        stats_panel = self.create_stats_panel()
        main_layout.addWidget(stats_panel)
        
        # لوحة التقارير
        reports_panel = self.create_reports_panel()
        main_layout.addWidget(reports_panel)
    
    def create_filters_panel(self):
        """إنشاء لوحة الفلاتر"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 15px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QHBoxLayout(panel)
        
        # فترة التقرير
        period_group = QGroupBox("فترة التقرير")
        period_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)
        
        period_layout = QHBoxLayout(period_group)
        
        # نوع الفترة
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "اليوم", "أمس", "هذا الأسبوع", "الأسبوع الماضي",
            "هذا الشهر", "الشهر الماضي", "هذا العام", "فترة مخصصة"
        ])
        self.period_combo.currentTextChanged.connect(self.on_period_changed)
        
        # تاريخ البداية
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate())
        self.start_date.setCalendarPopup(True)
        self.start_date.setEnabled(False)
        
        # تاريخ النهاية
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        self.end_date.setEnabled(False)
        
        period_layout.addWidget(QLabel("الفترة:"))
        period_layout.addWidget(self.period_combo)
        period_layout.addWidget(QLabel("من:"))
        period_layout.addWidget(self.start_date)
        period_layout.addWidget(QLabel("إلى:"))
        period_layout.addWidget(self.end_date)
        
        # زر التحديث
        refresh_button = QPushButton("🔄 تحديث التقرير")
        refresh_button.clicked.connect(self.refresh_reports)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        layout.addWidget(period_group, 3)
        layout.addWidget(refresh_button, 1)
        
        return panel
    
    def create_stats_panel(self):
        """إنشاء لوحة الإحصائيات السريعة"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 15px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QGridLayout(panel)
        
        # إحصائيات المبيعات
        self.create_stat_card(layout, 0, 0, "💰", "إجمالي المبيعات", "0.00 دج", "#27ae60")
        self.create_stat_card(layout, 0, 1, "🧾", "عدد الفواتير", "0", "#3498db")
        self.create_stat_card(layout, 0, 2, "📦", "المنتجات المباعة", "0", "#9b59b6")
        self.create_stat_card(layout, 0, 3, "👥", "العملاء الجدد", "0", "#e67e22")
        
        return panel
    
    def create_stat_card(self, layout, row, col, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setAlignment(Qt.AlignCenter)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 24px; color: white;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("color: white; font-weight: bold; font-size: 12px;")
        title_label.setAlignment(Qt.AlignCenter)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-weight: bold; font-size: 18px;")
        value_label.setAlignment(Qt.AlignCenter)
        
        card_layout.addWidget(icon_label)
        card_layout.addWidget(title_label)
        card_layout.addWidget(value_label)
        
        layout.addWidget(card, row, col)
    
    def create_reports_panel(self):
        """إنشاء لوحة التقارير"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 15px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        
        # شريط أزرار التقارير
        buttons_layout = QHBoxLayout()
        
        sales_report_btn = QPushButton("📊 تقرير المبيعات")
        sales_report_btn.clicked.connect(self.show_sales_report)
        
        products_report_btn = QPushButton("📦 تقرير المنتجات")
        products_report_btn.clicked.connect(self.show_products_report)
        
        customers_report_btn = QPushButton("👥 تقرير العملاء")
        customers_report_btn.clicked.connect(self.show_customers_report)
        
        inventory_report_btn = QPushButton("📋 تقرير المخزون")
        inventory_report_btn.clicked.connect(self.show_inventory_report)
        
        # تطبيق التنسيق على الأزرار
        button_style = """
            QPushButton {
                background-color: #34495e;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 15px;
                font-weight: bold;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #2c3e50;
            }
        """
        
        for btn in [sales_report_btn, products_report_btn, customers_report_btn, inventory_report_btn]:
            btn.setStyleSheet(button_style)
        
        buttons_layout.addWidget(sales_report_btn)
        buttons_layout.addWidget(products_report_btn)
        buttons_layout.addWidget(customers_report_btn)
        buttons_layout.addWidget(inventory_report_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # جدول التقرير
        self.report_table = QTableWidget()
        self.report_table.setStyleSheet("""
            QTableWidget {
                background-color: #e0e5ec;
                border: none;
                border-radius: 10px;
                gridline-color: #bdc3c7;
                selection-background-color: #3498db;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        
        self.report_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.report_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.report_table)
        
        # أزرار التصدير
        export_layout = QHBoxLayout()
        
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.clicked.connect(self.export_pdf)
        
        export_excel_btn = QPushButton("📊 تصدير Excel")
        export_excel_btn.clicked.connect(self.export_excel)
        
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.clicked.connect(self.print_report)
        
        export_layout.addStretch()
        export_layout.addWidget(export_pdf_btn)
        export_layout.addWidget(export_excel_btn)
        export_layout.addWidget(print_btn)
        
        layout.addLayout(export_layout)
        
        return panel
    
    def on_period_changed(self):
        """عند تغيير نوع الفترة"""
        is_custom = self.period_combo.currentText() == "فترة مخصصة"
        self.start_date.setEnabled(is_custom)
        self.end_date.setEnabled(is_custom)
    
    def refresh_reports(self):
        """تحديث التقارير"""
        QMessageBox.information(self, "تحديث التقارير", "تم تحديث التقارير")
    
    def show_sales_report(self):
        """عرض تقرير المبيعات"""
        self.report_table.setColumnCount(5)
        self.report_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "العميل", "المبلغ", "طريقة الدفع"
        ])
        
        # بيانات تجريبية
        sample_data = [
            ["INV-20250615-0001", "2025-06-15", "عميل نقدي", "1500.00", "نقدي"],
            ["INV-20250615-0002", "2025-06-15", "أحمد محمد", "2300.00", "بطاقة"],
            ["INV-20250615-0003", "2025-06-15", "فاطمة علي", "850.00", "نقدي"],
        ]
        
        self.populate_table(sample_data)
    
    def show_products_report(self):
        """عرض تقرير المنتجات"""
        self.report_table.setColumnCount(4)
        self.report_table.setHorizontalHeaderLabels([
            "المنتج", "الكمية المباعة", "الإيرادات", "الربح"
        ])
        
        sample_data = [
            ["منتج أ", "25", "1250.00", "250.00"],
            ["منتج ب", "15", "900.00", "180.00"],
            ["منتج ج", "30", "1500.00", "300.00"],
        ]
        
        self.populate_table(sample_data)
    
    def show_customers_report(self):
        """عرض تقرير العملاء"""
        QMessageBox.information(self, "تقرير العملاء", "تقرير العملاء قيد التطوير")
    
    def show_inventory_report(self):
        """عرض تقرير المخزون"""
        QMessageBox.information(self, "تقرير المخزون", "تقرير المخزون قيد التطوير")
    
    def populate_table(self, data):
        """ملء الجدول بالبيانات"""
        self.report_table.setRowCount(len(data))
        
        for row, row_data in enumerate(data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                self.report_table.setItem(row, col, item)
        
        # تعديل عرض الأعمدة
        header = self.report_table.horizontalHeader()
        header.setStretchLastSection(True)
    
    def export_pdf(self):
        """تصدير PDF"""
        QMessageBox.information(self, "تصدير PDF", "ميزة تصدير PDF قيد التطوير")
    
    def export_excel(self):
        """تصدير Excel"""
        QMessageBox.information(self, "تصدير Excel", "ميزة تصدير Excel قيد التطوير")
    
    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "الطباعة", "ميزة الطباعة قيد التطوير")
