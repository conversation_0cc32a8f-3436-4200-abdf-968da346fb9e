#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للنظام
"""

try:
    print("🔄 جاري تحميل النظام...")
    
    # اختبار الاستيرادات
    print("📦 اختبار استيراد المكتبات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 متاح")
    except ImportError as e:
        print(f"❌ PyQt5 غير متاح: {e}")
        print("💡 يرجى تثبيت PyQt5 باستخدام: pip install PyQt5")
        exit(1)
    
    try:
        import sqlite3
        print("✅ SQLite متاح")
    except ImportError as e:
        print(f"❌ SQLite غير متاح: {e}")
        exit(1)
    
    # اختبار قاعدة البيانات
    print("🗄️ اختبار قاعدة البيانات...")
    try:
        from database.database_manager import DatabaseManager
        db = DatabaseManager("test.db")
        if db.initialize_database():
            print("✅ قاعدة البيانات تعمل بشكل صحيح")
        else:
            print("❌ فشل في تهيئة قاعدة البيانات")
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
    
    # اختبار الواجهات
    print("🖥️ اختبار الواجهات...")
    try:
        from ui.login_window import LoginWindow
        print("✅ نافذة تسجيل الدخول متاحة")
    except Exception as e:
        print(f"❌ خطأ في نافذة تسجيل الدخول: {e}")
    
    try:
        from ui.main_window import MainWindow
        print("✅ النافذة الرئيسية متاحة")
    except Exception as e:
        print(f"❌ خطأ في النافذة الرئيسية: {e}")
    
    print("\n🎉 اختبار النظام مكتمل!")
    print("📋 لتشغيل النظام الكامل، استخدم: python main.py")
    
except Exception as e:
    print(f"💥 خطأ عام: {e}")
    import traceback
    traceback.print_exc()
