#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الواجهات الجديدة
Test New Interfaces
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# استيراد قاعدة البيانات
from database.database_manager import DatabaseManager

def test_imports():
    """اختبار استيراد جميع الواجهات الجديدة"""
    print("🧪 اختبار استيراد الواجهات...")
    
    try:
        from ui.touchscreen_interface import TouchscreenInterface
        print("✅ TouchscreenInterface - تم الاستيراد بنجاح")
    except Exception as e:
        print(f"❌ TouchscreenInterface - خطأ: {e}")
    
    try:
        from ui.cashier_interface import CashierInterface
        print("✅ CashierInterface - تم الاستيراد بنجاح")
    except Exception as e:
        print(f"❌ CashierInterface - خطأ: {e}")
    
    try:
        from ui.analytics_dashboard import AnalyticsDashboard
        print("✅ AnalyticsDashboard - تم الاستيراد بنجاح")
    except Exception as e:
        print(f"❌ AnalyticsDashboard - خطأ: {e}")
    
    try:
        from ui.suppliers_management import SuppliersManagement
        print("✅ SuppliersManagement - تم الاستيراد بنجاح")
    except Exception as e:
        print(f"❌ SuppliersManagement - خطأ: {e}")
    
    try:
        from ui.promotions_manager import PromotionsManager
        print("✅ PromotionsManager - تم الاستيراد بنجاح")
    except Exception as e:
        print(f"❌ PromotionsManager - خطأ: {e}")
    
    try:
        from ui.touch_payment_dialog import TouchCashPaymentDialog
        print("✅ TouchCashPaymentDialog - تم الاستيراد بنجاح")
    except Exception as e:
        print(f"❌ TouchCashPaymentDialog - خطأ: {e}")
    
    try:
        from utils.sound_manager import SoundManager
        print("✅ SoundManager - تم الاستيراد بنجاح")
    except Exception as e:
        print(f"❌ SoundManager - خطأ: {e}")
    
    try:
        from utils.payment_systems import PaymentManager
        print("✅ PaymentManager - تم الاستيراد بنجاح")
    except Exception as e:
        print(f"❌ PaymentManager - خطأ: {e}")

class TestWindow(QMainWindow):
    """نافذة اختبار الواجهات"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار الواجهات الجديدة")
        self.setGeometry(100, 100, 1000, 700)
        
        # إعداد قاعدة البيانات للاختبار
        self.db_manager = DatabaseManager()
        
        # بيانات مستخدم تجريبية
        self.user_data = {
            'id': 1,
            'username': 'test_user',
            'full_name': 'مستخدم تجريبي',
            'role': 'admin',
            'permissions': {
                'inventory': True,
                'reports': True,
                'settings': True
            }
        }
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        title = QLabel("🧪 اختبار الواجهات الجديدة")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2c3e50; margin: 20px;")
        layout.addWidget(title)
        
        # التبويبات
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #e0e5ec;
                border-radius: 10px;
            }
            QTabBar::tab {
                background-color: #e0e5ec;
                color: #2c3e50;
                padding: 12px 20px;
                margin: 2px;
                border-radius: 8px;
                font-weight: bold;
                min-width: 100px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        # إضافة التبويبات
        self.add_test_tabs(tab_widget)
        
        layout.addWidget(tab_widget)
    
    def add_test_tabs(self, tab_widget):
        """إضافة تبويبات الاختبار"""
        try:
            # تبويب الكاشير السريع
            from ui.cashier_interface import CashierInterface
            cashier_tab = CashierInterface(self.db_manager, self.user_data)
            tab_widget.addTab(cashier_tab, "⚡ كاشير سريع")
        except Exception as e:
            error_tab = self.create_error_tab(f"خطأ في كاشير سريع: {e}")
            tab_widget.addTab(error_tab, "❌ كاشير سريع")
        
        try:
            # تبويب الشاشة اللمسية
            from ui.touchscreen_interface import TouchscreenInterface
            touch_tab = TouchscreenInterface(self.db_manager, self.user_data)
            tab_widget.addTab(touch_tab, "📱 شاشة لمس")
        except Exception as e:
            error_tab = self.create_error_tab(f"خطأ في شاشة اللمس: {e}")
            tab_widget.addTab(error_tab, "❌ شاشة لمس")
        
        try:
            # تبويب التحليلات
            from ui.analytics_dashboard import AnalyticsDashboard
            analytics_tab = AnalyticsDashboard(self.db_manager, self.user_data)
            tab_widget.addTab(analytics_tab, "📊 التحليلات")
        except Exception as e:
            error_tab = self.create_error_tab(f"خطأ في التحليلات: {e}")
            tab_widget.addTab(error_tab, "❌ التحليلات")
        
        try:
            # تبويب الموردين
            from ui.suppliers_management import SuppliersManagement
            suppliers_tab = SuppliersManagement(self.db_manager, self.user_data)
            tab_widget.addTab(suppliers_tab, "🏭 الموردين")
        except Exception as e:
            error_tab = self.create_error_tab(f"خطأ في الموردين: {e}")
            tab_widget.addTab(error_tab, "❌ الموردين")
        
        try:
            # تبويب العروض
            from ui.promotions_manager import PromotionsManager
            promotions_tab = PromotionsManager(self.db_manager, self.user_data)
            tab_widget.addTab(promotions_tab, "🎯 العروض")
        except Exception as e:
            error_tab = self.create_error_tab(f"خطأ في العروض: {e}")
            tab_widget.addTab(error_tab, "❌ العروض")
    
    def create_error_tab(self, error_message):
        """إنشاء تبويب خطأ"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        error_label = QLabel(error_message)
        error_label.setStyleSheet("color: red; font-size: 14px; padding: 20px;")
        error_label.setWordWrap(True)
        error_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(error_label)
        
        return widget

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🧪 اختبار الواجهات الجديدة")
    print("=" * 50)
    
    # اختبار الاستيراد
    test_imports()
    
    print("\n" + "=" * 50)
    print("🚀 تشغيل نافذة الاختبار...")
    print("=" * 50)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = TestWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
