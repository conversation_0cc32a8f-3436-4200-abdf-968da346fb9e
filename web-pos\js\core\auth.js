/**
 * نظام المصادقة والتحقق
 * Authentication and Authorization System
 */

import { Storage } from './storage.js';
import { Utils } from './utils.js';

export class Auth {
    constructor() {
        this.storage = new Storage();
        this.utils = new Utils();
        this.currentUser = null;
        this.sessionTimeout = 8 * 60 * 60 * 1000; // 8 ساعات
    }

    /**
     * تسجيل الدخول
     */
    async login(username, password) {
        try {
            const users = this.storage.get('users') || [];
            const hashedPassword = await this.utils.hashPassword(password);
            
            const user = users.find(u => 
                u.username === username && 
                u.password === hashedPassword && 
                u.isActive
            );

            if (user) {
                // تحديث آخر تسجيل دخول
                user.lastLogin = new Date().toISOString();
                
                // حفظ المستخدمين المحدثين
                this.storage.set('users', users);
                
                // إنشاء جلسة
                const session = {
                    user: {
                        id: user.id,
                        username: user.username,
                        fullName: user.fullName,
                        role: user.role,
                        email: user.email,
                        phone: user.phone,
                        permissions: user.permissions
                    },
                    loginTime: new Date().toISOString(),
                    expiresAt: new Date(Date.now() + this.sessionTimeout).toISOString(),
                    sessionId: this.generateSessionId()
                };
                
                // حفظ الجلسة
                this.storage.set('currentSession', session);
                this.currentUser = session.user;
                
                // تسجيل نشاط تسجيل الدخول
                this.logActivity('login', `تسجيل دخول المستخدم ${user.fullName}`);
                
                return session.user;
            }
            
            // تسجيل محاولة دخول فاشلة
            this.logActivity('login_failed', `محاولة دخول فاشلة للمستخدم ${username}`);
            
            return null;
            
        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            throw error;
        }
    }

    /**
     * تسجيل الخروج
     */
    logout() {
        try {
            if (this.currentUser) {
                this.logActivity('logout', `تسجيل خروج المستخدم ${this.currentUser.fullName}`);
            }
            
            // مسح الجلسة
            this.storage.remove('currentSession');
            this.currentUser = null;
            
        } catch (error) {
            console.error('خطأ في تسجيل الخروج:', error);
        }
    }

    /**
     * الحصول على المستخدم الحالي
     */
    getCurrentUser() {
        try {
            if (this.currentUser) {
                return this.currentUser;
            }
            
            const session = this.storage.get('currentSession');
            if (session && this.isSessionValid(session)) {
                this.currentUser = session.user;
                return this.currentUser;
            }
            
            return null;
            
        } catch (error) {
            console.error('خطأ في الحصول على المستخدم الحالي:', error);
            return null;
        }
    }

    /**
     * فحص صحة الجلسة
     */
    isSessionValid(session = null) {
        try {
            const currentSession = session || this.storage.get('currentSession');
            
            if (!currentSession) {
                return false;
            }
            
            const now = new Date();
            const expiresAt = new Date(currentSession.expiresAt);
            
            if (now > expiresAt) {
                // انتهت صلاحية الجلسة
                this.logout();
                return false;
            }
            
            return true;
            
        } catch (error) {
            console.error('خطأ في فحص صحة الجلسة:', error);
            return false;
        }
    }

    /**
     * تجديد الجلسة
     */
    renewSession() {
        try {
            const session = this.storage.get('currentSession');
            
            if (session && this.isSessionValid(session)) {
                session.expiresAt = new Date(Date.now() + this.sessionTimeout).toISOString();
                this.storage.set('currentSession', session);
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.error('خطأ في تجديد الجلسة:', error);
            return false;
        }
    }

    /**
     * فحص الصلاحيات
     */
    hasPermission(permission) {
        try {
            const user = this.getCurrentUser();
            
            if (!user) {
                return false;
            }
            
            // المدير له جميع الصلاحيات
            if (user.role === 'admin') {
                return true;
            }
            
            // فحص الصلاحية المحددة
            return user.permissions && user.permissions[permission] === true;
            
        } catch (error) {
            console.error('خطأ في فحص الصلاحيات:', error);
            return false;
        }
    }

    /**
     * فحص الدور
     */
    hasRole(role) {
        try {
            const user = this.getCurrentUser();
            return user && user.role === role;
            
        } catch (error) {
            console.error('خطأ في فحص الدور:', error);
            return false;
        }
    }

    /**
     * تغيير كلمة المرور
     */
    async changePassword(currentPassword, newPassword) {
        try {
            const user = this.getCurrentUser();
            if (!user) {
                throw new Error('المستخدم غير مسجل الدخول');
            }
            
            const users = this.storage.get('users') || [];
            const userIndex = users.findIndex(u => u.id === user.id);
            
            if (userIndex === -1) {
                throw new Error('المستخدم غير موجود');
            }
            
            const hashedCurrentPassword = this.utils.hashPassword(currentPassword);
            if (users[userIndex].password !== hashedCurrentPassword) {
                throw new Error('كلمة المرور الحالية غير صحيحة');
            }
            
            // تحديث كلمة المرور
            users[userIndex].password = this.utils.hashPassword(newPassword);
            users[userIndex].passwordChangedAt = new Date().toISOString();
            
            this.storage.set('users', users);
            
            // تسجيل النشاط
            this.logActivity('password_changed', `تم تغيير كلمة المرور للمستخدم ${user.fullName}`);
            
            return true;
            
        } catch (error) {
            console.error('خطأ في تغيير كلمة المرور:', error);
            throw error;
        }
    }

    /**
     * إنشاء مستخدم جديد
     */
    async createUser(userData) {
        try {
            const currentUser = this.getCurrentUser();
            if (!currentUser || !this.hasPermission('settings')) {
                throw new Error('ليس لديك صلاحية لإنشاء مستخدمين جدد');
            }
            
            const users = this.storage.get('users') || [];
            
            // فحص وجود اسم المستخدم
            if (users.some(u => u.username === userData.username)) {
                throw new Error('اسم المستخدم موجود بالفعل');
            }
            
            // إنشاء المستخدم الجديد
            const newUser = {
                id: this.generateUserId(),
                username: userData.username,
                password: this.utils.hashPassword(userData.password),
                fullName: userData.fullName,
                role: userData.role,
                email: userData.email || '',
                phone: userData.phone || '',
                isActive: true,
                createdAt: new Date().toISOString(),
                createdBy: currentUser.id,
                lastLogin: null,
                permissions: userData.permissions || this.getDefaultPermissions(userData.role)
            };
            
            users.push(newUser);
            this.storage.set('users', users);
            
            // تسجيل النشاط
            this.logActivity('user_created', `تم إنشاء مستخدم جديد: ${newUser.fullName}`);
            
            return newUser;
            
        } catch (error) {
            console.error('خطأ في إنشاء المستخدم:', error);
            throw error;
        }
    }

    /**
     * تحديث بيانات المستخدم
     */
    async updateUser(userId, userData) {
        try {
            const currentUser = this.getCurrentUser();
            if (!currentUser || (!this.hasPermission('settings') && currentUser.id !== userId)) {
                throw new Error('ليس لديك صلاحية لتحديث بيانات المستخدم');
            }
            
            const users = this.storage.get('users') || [];
            const userIndex = users.findIndex(u => u.id === userId);
            
            if (userIndex === -1) {
                throw new Error('المستخدم غير موجود');
            }
            
            // تحديث البيانات
            const updatedUser = {
                ...users[userIndex],
                ...userData,
                updatedAt: new Date().toISOString(),
                updatedBy: currentUser.id
            };
            
            // عدم السماح بتغيير كلمة المرور من هنا
            delete updatedUser.password;
            
            users[userIndex] = updatedUser;
            this.storage.set('users', users);
            
            // تحديث الجلسة الحالية إذا كان المستخدم يحدث بياناته
            if (currentUser.id === userId) {
                const session = this.storage.get('currentSession');
                if (session) {
                    session.user = { ...session.user, ...userData };
                    this.storage.set('currentSession', session);
                    this.currentUser = session.user;
                }
            }
            
            // تسجيل النشاط
            this.logActivity('user_updated', `تم تحديث بيانات المستخدم: ${updatedUser.fullName}`);
            
            return updatedUser;
            
        } catch (error) {
            console.error('خطأ في تحديث المستخدم:', error);
            throw error;
        }
    }

    /**
     * حذف مستخدم
     */
    async deleteUser(userId) {
        try {
            const currentUser = this.getCurrentUser();
            if (!currentUser || !this.hasPermission('settings')) {
                throw new Error('ليس لديك صلاحية لحذف المستخدمين');
            }
            
            if (currentUser.id === userId) {
                throw new Error('لا يمكنك حذف حسابك الخاص');
            }
            
            const users = this.storage.get('users') || [];
            const userIndex = users.findIndex(u => u.id === userId);
            
            if (userIndex === -1) {
                throw new Error('المستخدم غير موجود');
            }
            
            const deletedUser = users[userIndex];
            
            // حذف المستخدم (أو تعطيله)
            users[userIndex].isActive = false;
            users[userIndex].deletedAt = new Date().toISOString();
            users[userIndex].deletedBy = currentUser.id;
            
            this.storage.set('users', users);
            
            // تسجيل النشاط
            this.logActivity('user_deleted', `تم حذف المستخدم: ${deletedUser.fullName}`);
            
            return true;
            
        } catch (error) {
            console.error('خطأ في حذف المستخدم:', error);
            throw error;
        }
    }

    /**
     * الحصول على جميع المستخدمين
     */
    getUsers() {
        try {
            const currentUser = this.getCurrentUser();
            if (!currentUser || !this.hasPermission('settings')) {
                return [];
            }
            
            const users = this.storage.get('users') || [];
            return users.filter(u => u.isActive).map(u => ({
                id: u.id,
                username: u.username,
                fullName: u.fullName,
                role: u.role,
                email: u.email,
                phone: u.phone,
                isActive: u.isActive,
                createdAt: u.createdAt,
                lastLogin: u.lastLogin
            }));
            
        } catch (error) {
            console.error('خطأ في الحصول على المستخدمين:', error);
            return [];
        }
    }

    /**
     * تسجيل النشاط
     */
    logActivity(action, description) {
        try {
            const activities = this.storage.get('activities') || [];
            const user = this.getCurrentUser();
            
            const activity = {
                id: this.generateActivityId(),
                action,
                description,
                userId: user ? user.id : null,
                userName: user ? user.fullName : 'غير معروف',
                timestamp: new Date().toISOString(),
                ip: 'localhost', // في بيئة الويب المحلية
                userAgent: navigator.userAgent
            };
            
            activities.unshift(activity);
            
            // الاحتفاظ بآخر 1000 نشاط فقط
            if (activities.length > 1000) {
                activities.splice(1000);
            }
            
            this.storage.set('activities', activities);
            
        } catch (error) {
            console.error('خطأ في تسجيل النشاط:', error);
        }
    }

    /**
     * الحصول على سجل الأنشطة
     */
    getActivities(limit = 100) {
        try {
            const currentUser = this.getCurrentUser();
            if (!currentUser || !this.hasPermission('settings')) {
                return [];
            }
            
            const activities = this.storage.get('activities') || [];
            return activities.slice(0, limit);
            
        } catch (error) {
            console.error('خطأ في الحصول على الأنشطة:', error);
            return [];
        }
    }

    /**
     * توليد معرف جلسة
     */
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).slice(2, 11);
    }

    /**
     * توليد معرف مستخدم
     */
    generateUserId() {
        const users = this.storage.get('users') || [];
        const maxId = users.reduce((max, user) => Math.max(max, user.id || 0), 0);
        return maxId + 1;
    }

    /**
     * توليد معرف نشاط
     */
    generateActivityId() {
        return 'activity_' + Date.now() + '_' + Math.random().toString(36).slice(2, 11);
    }

    /**
     * الحصول على الصلاحيات الافتراضية حسب الدور
     */
    getDefaultPermissions(role) {
        const permissions = {
            admin: {
                sales: true,
                inventory: true,
                customers: true,
                suppliers: true,
                promotions: true,
                reports: true,
                analytics: true,
                settings: true
            },
            manager: {
                sales: true,
                inventory: true,
                customers: true,
                suppliers: true,
                promotions: true,
                reports: true,
                analytics: true,
                settings: false
            },
            cashier: {
                sales: true,
                inventory: false,
                customers: true,
                suppliers: false,
                promotions: false,
                reports: false,
                analytics: false,
                settings: false
            }
        };
        
        return permissions[role] || permissions.cashier;
    }
}
