#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة الكاشير المبسطة - Simplified Cashier Interface
واجهة مبسطة وسريعة للكاشير مع التركيز على السرعة والكفاءة
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QPushButton, QFrame, QScrollArea,
                            QLineEdit, QSpinBox, QMessageBox, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QKeySequence
from PyQt5.QtWidgets import QShortcut
from .touchscreen_interface import TouchButton, TouchNumPad
from .touch_payment_dialog import TouchCashPaymentDialog

class QuickProductButton(TouchButton):
    """زر منتج سريع"""
    
    product_selected = pyqtSignal(dict)
    
    def __init__(self, product_data, parent=None):
        super().__init__(parent)
        self.product_data = product_data
        self.setup_product_button()
        
    def setup_product_button(self):
        """إعداد زر المنتج"""
        self.setFixedSize(120, 100)
        
        # تنسيق النص
        name = self.product_data['name'][:15] + "..." if len(self.product_data['name']) > 15 else self.product_data['name']
        price = f"{self.product_data['selling_price']:.0f} دج"
        stock = self.product_data['stock_quantity']
        
        button_text = f"{name}\n{price}"
        
        # تلوين حسب المخزون
        if stock <= 0:
            button_text += "\n❌ نفد"
            self.setEnabled(False)
            color = "#e74c3c"
        elif stock <= 5:
            button_text += f"\n⚠️ {stock}"
            color = "#f39c12"
        else:
            button_text += f"\n✅ {stock}"
            color = "#27ae60"
        
        self.setText(button_text)
        
        # تطبيق اللون
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 15px;
                padding: 8px;
                font-size: 10px;
                font-weight: bold;
                text-align: center;
            }}
            QPushButton:hover {{
                opacity: 0.8;
            }}
            QPushButton:pressed {{
                transform: scale(0.95);
            }}
        """)
        
        self.clicked.connect(lambda: self.product_selected.emit(self.product_data))

class QuickSaleItem(QFrame):
    """عنصر بيع سريع"""
    
    quantity_changed = pyqtSignal(int, int)  # product_id, new_quantity
    item_removed = pyqtSignal(int)  # product_id
    
    def __init__(self, product_data, quantity=1, parent=None):
        super().__init__(parent)
        self.product_data = product_data
        self.quantity = quantity
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة العنصر"""
        self.setFixedHeight(60)
        self.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border-radius: 10px;
                margin: 2px;
                padding: 5px;
                box-shadow: 2px 2px 4px #a3b1c6;
            }
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # معلومات المنتج
        product_info = QVBoxLayout()
        
        name_label = QLabel(self.product_data['name'])
        name_label.setFont(QFont("Arial", 10, QFont.Bold))
        name_label.setStyleSheet("color: #2c3e50;")
        product_info.addWidget(name_label)
        
        price_label = QLabel(f"{self.product_data['selling_price']:.2f} دج")
        price_label.setStyleSheet("color: #7f8c8d; font-size: 9px;")
        product_info.addWidget(price_label)
        
        layout.addLayout(product_info, 3)
        
        # الكمية
        quantity_layout = QHBoxLayout()
        
        minus_btn = QPushButton("−")
        minus_btn.setFixedSize(25, 25)
        minus_btn.clicked.connect(self.decrease_quantity)
        minus_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 12px;
                font-weight: bold;
            }
        """)
        
        self.quantity_label = QLabel(str(self.quantity))
        self.quantity_label.setAlignment(Qt.AlignCenter)
        self.quantity_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        self.quantity_label.setFixedWidth(30)
        
        plus_btn = QPushButton("+")
        plus_btn.setFixedSize(25, 25)
        plus_btn.clicked.connect(self.increase_quantity)
        plus_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 12px;
                font-weight: bold;
            }
        """)
        
        quantity_layout.addWidget(minus_btn)
        quantity_layout.addWidget(self.quantity_label)
        quantity_layout.addWidget(plus_btn)
        
        layout.addLayout(quantity_layout, 1)
        
        # المجموع
        total = self.quantity * self.product_data['selling_price']
        self.total_label = QLabel(f"{total:.2f} دج")
        self.total_label.setFont(QFont("Arial", 10, QFont.Bold))
        self.total_label.setStyleSheet("color: #27ae60;")
        self.total_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.total_label, 1)
        
        # زر الحذف
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(30, 30)
        delete_btn.clicked.connect(lambda: self.item_removed.emit(self.product_data['id']))
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 15px;
                font-size: 12px;
            }
        """)
        layout.addWidget(delete_btn)
    
    def increase_quantity(self):
        """زيادة الكمية"""
        max_stock = self.product_data['stock_quantity']
        if self.quantity < max_stock:
            self.quantity += 1
            self.update_display()
            self.quantity_changed.emit(self.product_data['id'], self.quantity)
    
    def decrease_quantity(self):
        """تقليل الكمية"""
        if self.quantity > 1:
            self.quantity -= 1
            self.update_display()
            self.quantity_changed.emit(self.product_data['id'], self.quantity)
    
    def update_display(self):
        """تحديث العرض"""
        self.quantity_label.setText(str(self.quantity))
        total = self.quantity * self.product_data['selling_price']
        self.total_label.setText(f"{total:.2f} دج")

class CashierInterface(QWidget):
    """واجهة الكاشير المبسطة"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.sale_items = {}  # product_id: QuickSaleItem
        self.current_total = 0.0
        self.setup_ui()
        self.setup_shortcuts()
        self.load_quick_products()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # الجانب الأيسر - المنتجات السريعة
        left_panel = self.create_products_panel()
        main_layout.addWidget(left_panel, 2)
        
        # الجانب الأوسط - الفاتورة
        middle_panel = self.create_invoice_panel()
        main_layout.addWidget(middle_panel, 2)
        
        # الجانب الأيمن - الدفع والعمليات
        right_panel = self.create_payment_panel()
        main_layout.addWidget(right_panel, 1)
    
    def create_products_panel(self):
        """إنشاء لوحة المنتجات"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 10px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        
        # عنوان وبحث سريع
        header_layout = QHBoxLayout()
        
        title = QLabel("⚡ منتجات سريعة")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #2c3e50;")
        header_layout.addWidget(title)
        
        # بحث سريع
        self.quick_search = QLineEdit()
        self.quick_search.setPlaceholderText("🔍 بحث سريع...")
        self.quick_search.setMaximumWidth(150)
        self.quick_search.setStyleSheet("""
            QLineEdit {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 5px 10px;
                font-size: 12px;
                box-shadow: inset 2px 2px 4px #a3b1c6, inset -2px -2px 4px #ffffff;
            }
        """)
        self.quick_search.textChanged.connect(self.filter_quick_products)
        header_layout.addWidget(self.quick_search)
        
        layout.addLayout(header_layout)
        
        # منطقة المنتجات
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.products_widget = QWidget()
        self.products_layout = QGridLayout(self.products_widget)
        self.products_layout.setSpacing(5)
        
        scroll_area.setWidget(self.products_widget)
        layout.addWidget(scroll_area)
        
        return panel
    
    def create_invoice_panel(self):
        """إنشاء لوحة الفاتورة"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 10px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        
        # عنوان الفاتورة
        header_layout = QHBoxLayout()
        
        title = QLabel("🧾 الفاتورة")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #2c3e50;")
        header_layout.addWidget(title)
        
        # رقم الفاتورة
        invoice_number = QLabel("فاتورة جديدة")
        invoice_number.setStyleSheet("color: #7f8c8d; font-size: 10px;")
        header_layout.addWidget(invoice_number)
        header_layout.addStretch()
        
        # زر مسح الكل
        clear_all_btn = QPushButton("🗑️ مسح الكل")
        clear_all_btn.clicked.connect(self.clear_all_items)
        clear_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 5px 10px;
                font-size: 10px;
                font-weight: bold;
            }
        """)
        header_layout.addWidget(clear_all_btn)
        
        layout.addLayout(header_layout)
        
        # منطقة عناصر الفاتورة
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.invoice_widget = QWidget()
        self.invoice_layout = QVBoxLayout(self.invoice_widget)
        self.invoice_layout.setSpacing(2)
        self.invoice_layout.addStretch()
        
        scroll_area.setWidget(self.invoice_widget)
        layout.addWidget(scroll_area)
        
        # المجاميع
        totals_frame = self.create_totals_frame()
        layout.addWidget(totals_frame)
        
        return panel
    
    def create_totals_frame(self):
        """إنشاء إطار المجاميع"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 10px;
                padding: 15px;
                margin-top: 5px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        layout.setSpacing(5)
        
        # المجموع الفرعي
        subtotal_layout = QHBoxLayout()
        subtotal_layout.addWidget(QLabel("المجموع الفرعي:"))
        self.subtotal_label = QLabel("0.00 دج")
        self.subtotal_label.setAlignment(Qt.AlignRight)
        subtotal_layout.addWidget(self.subtotal_label)
        layout.addLayout(subtotal_layout)
        
        # الضريبة
        tax_layout = QHBoxLayout()
        tax_layout.addWidget(QLabel("الضريبة (19%):"))
        self.tax_label = QLabel("0.00 دج")
        self.tax_label.setAlignment(Qt.AlignRight)
        tax_layout.addWidget(self.tax_label)
        layout.addLayout(tax_layout)
        
        # خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setStyleSheet("background-color: #7f8c8d;")
        layout.addWidget(line)
        
        # المجموع النهائي
        total_layout = QHBoxLayout()
        total_label = QLabel("المجموع:")
        total_label.setFont(QFont("Arial", 12, QFont.Bold))
        total_layout.addWidget(total_label)
        
        self.total_label = QLabel("0.00 دج")
        self.total_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.total_label.setStyleSheet("color: #f39c12;")
        self.total_label.setAlignment(Qt.AlignRight)
        total_layout.addWidget(self.total_label)
        layout.addLayout(total_layout)
        
        # تطبيق التنسيق على جميع التسميات
        for i in range(layout.count()):
            item = layout.itemAt(i)
            if item and hasattr(item, 'layout'):
                for j in range(item.layout().count()):
                    widget = item.layout().itemAt(j).widget()
                    if isinstance(widget, QLabel):
                        widget.setStyleSheet("color: white; font-size: 11px;")
        
        return frame
    
    def create_payment_panel(self):
        """إنشاء لوحة الدفع"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 10px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setSpacing(10)
        
        # عنوان
        title = QLabel("💳 الدفع")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #2c3e50;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # أزرار الدفع
        self.cash_btn = TouchButton("💵\nنقدي")
        self.cash_btn.setMinimumHeight(60)
        self.cash_btn.clicked.connect(self.process_cash_payment)
        self.cash_btn.setEnabled(False)
        self.cash_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        layout.addWidget(self.cash_btn)
        
        self.card_btn = TouchButton("💳\nبطاقة")
        self.card_btn.setMinimumHeight(60)
        self.card_btn.clicked.connect(self.process_card_payment)
        self.card_btn.setEnabled(False)
        self.card_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        layout.addWidget(self.card_btn)
        
        # أزرار إضافية
        hold_btn = TouchButton("⏸️\nتعليق")
        hold_btn.clicked.connect(self.hold_sale)
        layout.addWidget(hold_btn)
        
        recall_btn = TouchButton("📋\nاستدعاء")
        recall_btn.clicked.connect(self.recall_sale)
        layout.addWidget(recall_btn)
        
        # لوحة أرقام مصغرة
        mini_numpad = self.create_mini_numpad()
        layout.addWidget(mini_numpad)
        
        layout.addStretch()
        
        return panel

    def create_mini_numpad(self):
        """إنشاء لوحة أرقام مصغرة"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border-radius: 10px;
                padding: 5px;
            }
        """)

        layout = QGridLayout(frame)
        layout.setSpacing(2)

        # أزرار الأرقام
        numbers = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0']

        for i, num in enumerate(numbers):
            row = i // 3
            col = i % 3

            btn = QPushButton(num)
            btn.setFixedSize(30, 30)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #e0e5ec;
                    border: none;
                    border-radius: 15px;
                    font-weight: bold;
                    font-size: 12px;
                }
                QPushButton:pressed {
                    background-color: #bdc3c7;
                }
            """)

            if i == 9:  # الرقم 0
                layout.addWidget(btn, 3, 1)
            else:
                layout.addWidget(btn, row, col)

        # زر مسح
        clear_btn = QPushButton("C")
        clear_btn.setFixedSize(30, 30)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 15px;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        layout.addWidget(clear_btn, 3, 0)

        # زر إدخال
        enter_btn = QPushButton("✓")
        enter_btn.setFixedSize(30, 30)
        enter_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 15px;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        layout.addWidget(enter_btn, 3, 2)

        return frame

    def setup_shortcuts(self):
        """إعداد الاختصارات"""
        # F1 - بيع جديد
        QShortcut(QKeySequence("F1"), self, self.new_sale)

        # F4 - دفع نقدي
        QShortcut(QKeySequence("F4"), self, self.process_cash_payment)

        # F5 - دفع بالبطاقة
        QShortcut(QKeySequence("F5"), self, self.process_card_payment)

        # Delete - مسح الكل
        QShortcut(QKeySequence("Delete"), self, self.clear_all_items)

        # Enter - تركيز على البحث
        QShortcut(QKeySequence("Return"), self, lambda: self.quick_search.setFocus())

    def load_quick_products(self):
        """تحميل المنتجات السريعة"""
        try:
            # تحميل المنتجات الأكثر مبيعاً أو المفضلة
            products = self.db_manager.get_all_products()

            # أخذ أول 20 منتج للعرض السريع
            quick_products = products[:20] if len(products) > 20 else products

            self.display_quick_products(quick_products)

        except Exception as e:
            print(f"خطأ في تحميل المنتجات السريعة: {e}")

    def display_quick_products(self, products):
        """عرض المنتجات السريعة"""
        # مسح المنتجات الحالية
        for i in reversed(range(self.products_layout.count())):
            child = self.products_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # عرض المنتجات الجديدة
        cols = 4  # عدد الأعمدة
        for index, product in enumerate(products):
            row = index // cols
            col = index % cols

            product_btn = QuickProductButton(product)
            product_btn.product_selected.connect(self.add_product_to_sale)
            self.products_layout.addWidget(product_btn, row, col)

    def filter_quick_products(self):
        """فلترة المنتجات السريعة"""
        search_text = self.quick_search.text().lower()

        for i in range(self.products_layout.count()):
            item = self.products_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if isinstance(widget, QuickProductButton):
                    product_name = widget.product_data['name'].lower()
                    barcode = widget.product_data.get('barcode', '').lower()

                    visible = (search_text in product_name or
                             search_text in barcode or
                             search_text == "")

                    widget.setVisible(visible)

    def add_product_to_sale(self, product):
        """إضافة منتج للبيع"""
        product_id = product['id']

        if product_id in self.sale_items:
            # زيادة الكمية إذا كان المنتج موجود
            current_item = self.sale_items[product_id]
            if current_item.quantity < product['stock_quantity']:
                current_item.increase_quantity()
        else:
            # إضافة منتج جديد
            if product['stock_quantity'] > 0:
                sale_item = QuickSaleItem(product)
                sale_item.quantity_changed.connect(self.on_quantity_changed)
                sale_item.item_removed.connect(self.remove_item)

                # إدراج في بداية القائمة
                self.invoice_layout.insertWidget(0, sale_item)
                self.sale_items[product_id] = sale_item

                self.update_totals()

    def on_quantity_changed(self, product_id, new_quantity):
        """عند تغيير الكمية"""
        self.update_totals()

    def remove_item(self, product_id):
        """حذف عنصر"""
        if product_id in self.sale_items:
            item = self.sale_items[product_id]
            item.setParent(None)
            del self.sale_items[product_id]
            self.update_totals()

    def clear_all_items(self):
        """مسح جميع العناصر"""
        if not self.sale_items:
            return

        reply = QMessageBox.question(
            self, "مسح الفاتورة",
            "هل أنت متأكد من مسح جميع العناصر؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            for item in self.sale_items.values():
                item.setParent(None)

            self.sale_items.clear()
            self.update_totals()

    def update_totals(self):
        """تحديث المجاميع"""
        subtotal = 0.0

        for item in self.sale_items.values():
            item_total = item.quantity * item.product_data['selling_price']
            subtotal += item_total

        tax_amount = subtotal * 0.19  # 19% ضريبة
        total = subtotal + tax_amount

        self.subtotal_label.setText(f"{subtotal:.2f} دج")
        self.tax_label.setText(f"{tax_amount:.2f} دج")
        self.total_label.setText(f"{total:.2f} دج")

        self.current_total = total

        # تفعيل أزرار الدفع
        has_items = len(self.sale_items) > 0
        self.cash_btn.setEnabled(has_items)
        self.card_btn.setEnabled(has_items)

    def process_cash_payment(self):
        """معالجة الدفع النقدي"""
        if not self.sale_items:
            QMessageBox.warning(self, "تحذير", "لا توجد عناصر في الفاتورة")
            return

        dialog = TouchCashPaymentDialog(self.current_total, self)
        if dialog.exec_() == dialog.Accepted:
            payment_data = dialog.get_payment_data()
            self.complete_sale(payment_data)

    def process_card_payment(self):
        """معالجة الدفع بالبطاقة"""
        if not self.sale_items:
            QMessageBox.warning(self, "تحذير", "لا توجد عناصر في الفاتورة")
            return

        reply = QMessageBox.question(
            self, "الدفع بالبطاقة",
            f"المبلغ: {self.current_total:.2f} دج\nهل تم الدفع بنجاح؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            payment_data = {
                'method': 'بطاقة',
                'amount': self.current_total,
                'paid_amount': self.current_total,
                'change': 0.0
            }
            self.complete_sale(payment_data)

    def complete_sale(self, payment_data):
        """إتمام البيع"""
        try:
            # إعداد بيانات البيع
            sale_items = []
            for item in self.sale_items.values():
                sale_items.append({
                    'product_id': item.product_data['id'],
                    'name': item.product_data['name'],
                    'quantity': item.quantity,
                    'unit_price': item.product_data['selling_price'],
                    'total_price': item.quantity * item.product_data['selling_price']
                })

            sale_data = {
                'user_id': self.user_data['id'],
                'total_amount': self.current_total,
                'paid_amount': payment_data['paid_amount'],
                'change_amount': payment_data.get('change', 0.0),
                'payment_method': payment_data['method']
            }

            # حفظ البيع
            invoice_number = self.db_manager.create_sale(sale_data, sale_items)

            if invoice_number:
                QMessageBox.information(
                    self, "تم البيع",
                    f"رقم الفاتورة: {invoice_number}\nالمبلغ: {self.current_total:.2f} دج"
                )

                self.new_sale()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ البيع")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    def new_sale(self):
        """بيع جديد"""
        self.clear_all_items()
        self.quick_search.clear()
        self.quick_search.setFocus()

    def hold_sale(self):
        """تعليق البيع"""
        if not self.sale_items:
            QMessageBox.warning(self, "تحذير", "لا توجد عناصر للتعليق")
            return

        QMessageBox.information(self, "تعليق البيع", "ميزة تعليق البيع قيد التطوير")

    def recall_sale(self):
        """استدعاء بيع معلق"""
        QMessageBox.information(self, "استدعاء البيع", "ميزة استدعاء البيع قيد التطوير")
