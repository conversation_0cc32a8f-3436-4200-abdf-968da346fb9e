#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة الدفع للمس - Touch Payment Dialog
نافذة دفع محسنة لشاشات اللمس
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QFrame, QMessageBox, QPushButton)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from .touchscreen_interface import TouchButton, TouchNumPad

class TouchCashPaymentDialog(QDialog):
    """نافذة الدفع النقدي للمس"""
    
    def __init__(self, total_amount, parent=None):
        super().__init__(parent)
        self.total_amount = total_amount
        self.paid_amount = 0.0
        self.payment_data = {}
        self.current_input = ""
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("الدفع النقدي")
        self.setFixedSize(800, 600)
        self.setModal(True)
        
        # إزالة إطار النافذة للمس الكامل
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # شريط العنوان
        header = self.create_header()
        main_layout.addWidget(header)
        
        # المحتوى الرئيسي
        content_layout = QHBoxLayout()
        
        # الجانب الأيسر - المعلومات والمبالغ
        left_panel = self.create_info_panel()
        content_layout.addWidget(left_panel, 2)
        
        # الجانب الأيمن - لوحة الأرقام
        right_panel = self.create_numpad_panel()
        content_layout.addWidget(right_panel, 1)
        
        main_layout.addLayout(content_layout)
        
        # أزرار العمل
        actions_panel = self.create_actions_panel()
        main_layout.addWidget(actions_panel)
    
    def create_header(self):
        """إنشاء شريط العنوان"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 15px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(header)
        
        # العنوان
        title = QLabel("💵 الدفع النقدي")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setStyleSheet("color: white;")
        layout.addWidget(title)
        
        layout.addStretch()
        
        # زر الإغلاق
        close_btn = TouchButton("✖")
        close_btn.setMaximumSize(50, 50)
        close_btn.clicked.connect(self.reject)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 25px;
                font-size: 18px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        layout.addWidget(close_btn)
        
        return header
    
    def create_info_panel(self):
        """إنشاء لوحة المعلومات"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 20px;
                padding: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setSpacing(20)
        
        # المبلغ المطلوب
        required_frame = QFrame()
        required_frame.setStyleSheet("""
            QFrame {
                background-color: #e74c3c;
                border-radius: 15px;
                padding: 20px;
            }
        """)
        required_layout = QVBoxLayout(required_frame)
        
        required_label = QLabel("المبلغ المطلوب")
        required_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        required_label.setAlignment(Qt.AlignCenter)
        
        self.required_amount_label = QLabel(f"{self.total_amount:.2f} دج")
        self.required_amount_label.setStyleSheet("color: white; font-size: 24px; font-weight: bold;")
        self.required_amount_label.setAlignment(Qt.AlignCenter)
        
        required_layout.addWidget(required_label)
        required_layout.addWidget(self.required_amount_label)
        layout.addWidget(required_frame)
        
        # المبلغ المدفوع
        paid_frame = QFrame()
        paid_frame.setStyleSheet("""
            QFrame {
                background-color: #3498db;
                border-radius: 15px;
                padding: 20px;
            }
        """)
        paid_layout = QVBoxLayout(paid_frame)
        
        paid_label = QLabel("المبلغ المدفوع")
        paid_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        paid_label.setAlignment(Qt.AlignCenter)
        
        self.paid_amount_label = QLabel("0.00 دج")
        self.paid_amount_label.setStyleSheet("color: white; font-size: 24px; font-weight: bold;")
        self.paid_amount_label.setAlignment(Qt.AlignCenter)
        
        paid_layout.addWidget(paid_label)
        paid_layout.addWidget(self.paid_amount_label)
        layout.addWidget(paid_frame)
        
        # الباقي
        change_frame = QFrame()
        change_frame.setStyleSheet("""
            QFrame {
                background-color: #27ae60;
                border-radius: 15px;
                padding: 20px;
            }
        """)
        change_layout = QVBoxLayout(change_frame)
        
        change_label = QLabel("الباقي")
        change_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        change_label.setAlignment(Qt.AlignCenter)
        
        self.change_label = QLabel("0.00 دج")
        self.change_label.setStyleSheet("color: white; font-size: 24px; font-weight: bold;")
        self.change_label.setAlignment(Qt.AlignCenter)
        
        change_layout.addWidget(change_label)
        change_layout.addWidget(self.change_label)
        layout.addWidget(change_frame)
        
        # المبالغ السريعة
        quick_amounts_frame = self.create_quick_amounts()
        layout.addWidget(quick_amounts_frame)
        
        return panel
    
    def create_quick_amounts(self):
        """إنشاء أزرار المبالغ السريعة"""
        frame = QFrame()
        layout = QVBoxLayout(frame)
        
        title = QLabel("مبالغ سريعة")
        title.setStyleSheet("color: #2c3e50; font-size: 16px; font-weight: bold;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # الصف الأول
        row1 = QHBoxLayout()
        amounts1 = [100, 200, 500, 1000]
        for amount in amounts1:
            btn = TouchButton(f"{amount}")
            btn.setMinimumHeight(60)
            btn.clicked.connect(lambda checked, a=amount: self.set_amount(a))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #9b59b6;
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #8e44ad;
                }
            """)
            row1.addWidget(btn)
        
        # الصف الثاني
        row2 = QHBoxLayout()
        amounts2 = [2000, 5000, 10000]
        for amount in amounts2:
            btn = TouchButton(f"{amount}")
            btn.setMinimumHeight(60)
            btn.clicked.connect(lambda checked, a=amount: self.set_amount(a))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #9b59b6;
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #8e44ad;
                }
            """)
            row2.addWidget(btn)
        
        # زر المبلغ الدقيق
        exact_btn = TouchButton("المبلغ الدقيق")
        exact_btn.setMinimumHeight(60)
        exact_btn.clicked.connect(lambda: self.set_amount(self.total_amount))
        exact_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        row2.addWidget(exact_btn)
        
        layout.addLayout(row1)
        layout.addLayout(row2)
        
        return frame
    
    def create_numpad_panel(self):
        """إنشاء لوحة الأرقام"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 20px;
                padding: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(panel)
        
        title = QLabel("لوحة الأرقام")
        title.setStyleSheet("color: #2c3e50; font-size: 16px; font-weight: bold;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # شاشة الإدخال
        self.input_display = QLabel("0")
        self.input_display.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                font-size: 20px;
                font-weight: bold;
                padding: 15px;
                border-radius: 10px;
                border: 2px solid #34495e;
            }
        """)
        self.input_display.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        layout.addWidget(self.input_display)
        
        # لوحة الأرقام
        self.numpad = TouchNumPad()
        self.numpad.number_entered.connect(self.on_number_entered)
        self.numpad.clear_pressed.connect(self.clear_input)
        self.numpad.enter_pressed.connect(self.apply_input)
        layout.addWidget(self.numpad)
        
        return panel
    
    def create_actions_panel(self):
        """إنشاء لوحة الأزرار"""
        panel = QFrame()
        layout = QHBoxLayout(panel)
        layout.setSpacing(20)
        
        # زر الإلغاء
        cancel_btn = TouchButton("❌ إلغاء")
        cancel_btn.setMinimumHeight(80)
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        layout.addWidget(cancel_btn)
        
        # زر التأكيد
        self.confirm_btn = TouchButton("✅ تأكيد الدفع")
        self.confirm_btn.setMinimumHeight(80)
        self.confirm_btn.clicked.connect(self.confirm_payment)
        self.confirm_btn.setEnabled(False)
        self.confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        layout.addWidget(self.confirm_btn)
        
        return panel
    
    def on_number_entered(self, number):
        """عند إدخال رقم"""
        if self.current_input == "0":
            self.current_input = number
        else:
            self.current_input += number
        
        self.input_display.setText(self.current_input)
    
    def clear_input(self):
        """مسح الإدخال"""
        if len(self.current_input) > 1:
            self.current_input = self.current_input[:-1]
        else:
            self.current_input = "0"
        
        self.input_display.setText(self.current_input)
    
    def apply_input(self):
        """تطبيق الإدخال"""
        try:
            amount = float(self.current_input)
            self.set_amount(amount)
        except ValueError:
            pass
    
    def set_amount(self, amount):
        """تعيين المبلغ"""
        self.paid_amount = amount
        self.paid_amount_label.setText(f"{amount:.2f} دج")
        
        # حساب الباقي
        change = amount - self.total_amount
        self.change_label.setText(f"{change:.2f} دج")
        
        # تغيير لون الباقي
        if change < 0:
            self.change_label.setStyleSheet("color: white; font-size: 24px; font-weight: bold;")
            self.change_label.parent().setStyleSheet("""
                QFrame {
                    background-color: #e74c3c;
                    border-radius: 15px;
                    padding: 20px;
                }
            """)
            self.confirm_btn.setEnabled(False)
        else:
            self.change_label.setStyleSheet("color: white; font-size: 24px; font-weight: bold;")
            self.change_label.parent().setStyleSheet("""
                QFrame {
                    background-color: #27ae60;
                    border-radius: 15px;
                    padding: 20px;
                }
            """)
            self.confirm_btn.setEnabled(True)
        
        # تحديث الإدخال
        self.current_input = str(amount)
        self.input_display.setText(self.current_input)
    
    def confirm_payment(self):
        """تأكيد الدفع"""
        if self.paid_amount < self.total_amount:
            QMessageBox.warning(self, "مبلغ غير كافي", 
                              f"المبلغ المدفوع أقل من المطلوب\nالنقص: {self.total_amount - self.paid_amount:.2f} دج")
            return
        
        change = self.paid_amount - self.total_amount
        
        self.payment_data = {
            'method': 'نقدي',
            'amount': self.total_amount,
            'paid_amount': self.paid_amount,
            'change': change
        }
        
        self.accept()
    
    def get_payment_data(self):
        """الحصول على بيانات الدفع"""
        return self.payment_data
