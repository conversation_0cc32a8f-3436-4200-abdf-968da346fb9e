#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
لوحة التحكم التحليلية - Analytics Dashboard
لوحة تحكم متقدمة للإحصائيات والتحليلات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QFrame, QPushButton, QComboBox, QDateEdit,
                            QScrollArea, QProgressBar, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPainter, QPen, QBrush, QColor
import random
from datetime import datetime, timedelta

class MetricCard(QFrame):
    """بطاقة مقياس إحصائي"""
    
    def __init__(self, title, value, icon, color, trend=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.icon = icon
        self.color = color
        self.trend = trend
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة البطاقة"""
        self.setFixedSize(250, 150)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {self.color};
                border-radius: 15px;
                padding: 15px;
                margin: 5px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # الصف العلوي - الأيقونة والاتجاه
        top_row = QHBoxLayout()
        
        # الأيقونة
        icon_label = QLabel(self.icon)
        icon_label.setStyleSheet("font-size: 24px; color: white;")
        top_row.addWidget(icon_label)
        
        top_row.addStretch()
        
        # اتجاه التغيير
        if self.trend:
            trend_label = QLabel(self.trend)
            trend_label.setStyleSheet("color: white; font-size: 12px;")
            top_row.addWidget(trend_label)
        
        layout.addLayout(top_row)
        
        # القيمة الرئيسية
        value_label = QLabel(str(self.value))
        value_label.setStyleSheet("color: white; font-size: 28px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(self.title)
        title_label.setStyleSheet("color: white; font-size: 14px; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        layout.addWidget(title_label)

class SimpleChart(QWidget):
    """رسم بياني بسيط"""
    
    def __init__(self, data, chart_type="bar", title="", parent=None):
        super().__init__(parent)
        self.data = data
        self.chart_type = chart_type
        self.title = title
        self.setMinimumSize(300, 200)
        
    def paintEvent(self, event):
        """رسم الرسم البياني"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية
        painter.fillRect(self.rect(), QColor("#e0e5ec"))
        
        if not self.data:
            return
        
        # هوامش
        margin = 40
        chart_rect = self.rect().adjusted(margin, margin, -margin, -margin)
        
        if self.chart_type == "bar":
            self.draw_bar_chart(painter, chart_rect)
        elif self.chart_type == "line":
            self.draw_line_chart(painter, chart_rect)
        elif self.chart_type == "pie":
            self.draw_pie_chart(painter, chart_rect)
    
    def draw_bar_chart(self, painter, rect):
        """رسم رسم بياني بالأعمدة"""
        if not self.data:
            return
        
        max_value = max(self.data.values()) if self.data.values() else 1
        bar_width = rect.width() // len(self.data)
        
        colors = ["#3498db", "#e74c3c", "#2ecc71", "#f39c12", "#9b59b6"]
        
        for i, (label, value) in enumerate(self.data.items()):
            x = rect.left() + i * bar_width
            height = int((value / max_value) * rect.height())
            y = rect.bottom() - height
            
            # رسم العمود
            color = QColor(colors[i % len(colors)])
            painter.fillRect(x + 5, y, bar_width - 10, height, color)
            
            # رسم القيمة
            painter.setPen(QPen(QColor("#2c3e50")))
            painter.drawText(x, y - 5, bar_width, 20, Qt.AlignCenter, str(value))
            
            # رسم التسمية
            painter.drawText(x, rect.bottom() + 5, bar_width, 20, Qt.AlignCenter, label[:8])
    
    def draw_line_chart(self, painter, rect):
        """رسم رسم بياني خطي"""
        if len(self.data) < 2:
            return
        
        max_value = max(self.data.values()) if self.data.values() else 1
        points = []
        
        for i, (label, value) in enumerate(self.data.items()):
            x = rect.left() + (i * rect.width()) // (len(self.data) - 1)
            y = rect.bottom() - int((value / max_value) * rect.height())
            points.append((x, y))
        
        # رسم الخط
        painter.setPen(QPen(QColor("#3498db"), 3))
        for i in range(len(points) - 1):
            painter.drawLine(points[i][0], points[i][1], points[i+1][0], points[i+1][1])
        
        # رسم النقاط
        painter.setBrush(QBrush(QColor("#e74c3c")))
        for x, y in points:
            painter.drawEllipse(x-4, y-4, 8, 8)
    
    def draw_pie_chart(self, painter, rect):
        """رسم رسم بياني دائري"""
        if not self.data:
            return
        
        total = sum(self.data.values())
        if total == 0:
            return
        
        colors = ["#3498db", "#e74c3c", "#2ecc71", "#f39c12", "#9b59b6"]
        start_angle = 0
        
        # تحديد مركز ونصف قطر الدائرة
        center_x = rect.center().x()
        center_y = rect.center().y()
        radius = min(rect.width(), rect.height()) // 2 - 10
        
        for i, (label, value) in enumerate(self.data.items()):
            angle = int((value / total) * 360 * 16)  # Qt uses 1/16th degrees
            
            color = QColor(colors[i % len(colors)])
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(QColor("#ffffff"), 2))
            
            painter.drawPie(center_x - radius, center_y - radius, 
                          radius * 2, radius * 2, start_angle, angle)
            
            start_angle += angle

class AnalyticsDashboard(QWidget):
    """لوحة التحكم التحليلية الرئيسية"""
    
    refresh_requested = pyqtSignal()
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.setup_ui()
        self.setup_auto_refresh()
        self.load_analytics_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # شريط العنوان والفلاتر
        header = self.create_header()
        main_layout.addWidget(header)
        
        # منطقة التمرير للمحتوى
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # محتوى لوحة التحكم
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)
        
        # بطاقات المقاييس الرئيسية
        metrics_section = self.create_metrics_section()
        content_layout.addWidget(metrics_section)
        
        # الرسوم البيانية
        charts_section = self.create_charts_section()
        content_layout.addWidget(charts_section)
        
        # الجداول التحليلية
        tables_section = self.create_tables_section()
        content_layout.addWidget(tables_section)
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
    
    def create_header(self):
        """إنشاء شريط العنوان"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 15px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(header)
        
        # العنوان
        title = QLabel("📊 لوحة التحكم التحليلية")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: white;")
        layout.addWidget(title)
        
        layout.addStretch()
        
        # فلتر الفترة الزمنية
        period_label = QLabel("الفترة:")
        period_label.setStyleSheet("color: white; font-weight: bold;")
        layout.addWidget(period_label)
        
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "اليوم", "أمس", "هذا الأسبوع", "الأسبوع الماضي",
            "هذا الشهر", "الشهر الماضي", "آخر 30 يوم", "هذا العام"
        ])
        self.period_combo.setStyleSheet("""
            QComboBox {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
                font-weight: bold;
                min-width: 120px;
            }
        """)
        self.period_combo.currentTextChanged.connect(self.on_period_changed)
        layout.addWidget(self.period_combo)
        
        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_data)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(refresh_btn)
        
        return header
    
    def create_metrics_section(self):
        """إنشاء قسم المقاييس"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(section)
        
        # عنوان القسم
        title = QLabel("📈 المقاييس الرئيسية")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        layout.addWidget(title)
        
        # شبكة البطاقات
        cards_layout = QGridLayout()
        cards_layout.setSpacing(15)
        
        # بطاقات المقاييس
        self.revenue_card = MetricCard("إجمالي الإيرادات", "0.00 دج", "💰", "#27ae60", "↗️ +12%")
        self.sales_card = MetricCard("عدد المبيعات", "0", "🛒", "#3498db", "↗️ +8%")
        self.customers_card = MetricCard("العملاء الجدد", "0", "👥", "#9b59b6", "↗️ +15%")
        self.products_card = MetricCard("المنتجات المباعة", "0", "📦", "#e67e22", "↘️ -3%")
        
        cards_layout.addWidget(self.revenue_card, 0, 0)
        cards_layout.addWidget(self.sales_card, 0, 1)
        cards_layout.addWidget(self.customers_card, 1, 0)
        cards_layout.addWidget(self.products_card, 1, 1)
        
        layout.addLayout(cards_layout)
        
        return section
    
    def create_charts_section(self):
        """إنشاء قسم الرسوم البيانية"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(section)
        
        # عنوان القسم
        title = QLabel("📊 الرسوم البيانية")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        layout.addWidget(title)
        
        # شبكة الرسوم البيانية
        charts_layout = QGridLayout()
        charts_layout.setSpacing(15)
        
        # رسم بياني للمبيعات اليومية
        daily_sales_data = {
            "السبت": 1500, "الأحد": 2300, "الاثنين": 1800,
            "الثلاثاء": 2100, "الأربعاء": 1900, "الخميس": 2500, "الجمعة": 2800
        }
        self.daily_sales_chart = SimpleChart(daily_sales_data, "bar", "المبيعات اليومية")
        charts_layout.addWidget(self.daily_sales_chart, 0, 0)
        
        # رسم بياني للمنتجات الأكثر مبيعاً
        top_products_data = {
            "منتج أ": 35, "منتج ب": 25, "منتج ج": 20, "منتج د": 15, "أخرى": 5
        }
        self.top_products_chart = SimpleChart(top_products_data, "pie", "المنتجات الأكثر مبيعاً")
        charts_layout.addWidget(self.top_products_chart, 0, 1)
        
        # رسم بياني لاتجاه المبيعات
        sales_trend_data = {
            "يناير": 15000, "فبراير": 18000, "مارس": 22000,
            "أبريل": 19000, "مايو": 25000, "يونيو": 28000
        }
        self.sales_trend_chart = SimpleChart(sales_trend_data, "line", "اتجاه المبيعات الشهرية")
        charts_layout.addWidget(self.sales_trend_chart, 1, 0, 1, 2)
        
        layout.addLayout(charts_layout)
        
        return section
    
    def create_tables_section(self):
        """إنشاء قسم الجداول"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        layout = QVBoxLayout(section)
        
        # عنوان القسم
        title = QLabel("📋 التحليلات التفصيلية")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        layout.addWidget(title)
        
        # تخطيط الجداول
        tables_layout = QHBoxLayout()
        
        # جدول أفضل المنتجات
        self.top_products_table = self.create_top_products_table()
        tables_layout.addWidget(self.top_products_table)
        
        # جدول أفضل العملاء
        self.top_customers_table = self.create_top_customers_table()
        tables_layout.addWidget(self.top_customers_table)
        
        layout.addLayout(tables_layout)
        
        return section
    
    def create_top_products_table(self):
        """إنشاء جدول أفضل المنتجات"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title = QLabel("🏆 أفضل المنتجات")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title)
        
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["المنتج", "المبيعات", "الإيرادات"])
        
        # بيانات تجريبية
        products_data = [
            ["منتج أ", "150", "15,000 دج"],
            ["منتج ب", "120", "12,000 دج"],
            ["منتج ج", "100", "10,000 دج"],
            ["منتج د", "85", "8,500 دج"],
            ["منتج هـ", "70", "7,000 دج"]
        ]
        
        table.setRowCount(len(products_data))
        for row, data in enumerate(products_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, col, item)
        
        table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: none;
                gridline-color: #ecf0f1;
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setMaximumHeight(200)
        
        layout.addWidget(table)
        
        return frame
    
    def create_top_customers_table(self):
        """إنشاء جدول أفضل العملاء"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title = QLabel("⭐ أفضل العملاء")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title)
        
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["العميل", "المشتريات", "المبلغ"])
        
        # بيانات تجريبية
        customers_data = [
            ["أحمد محمد", "25", "5,000 دج"],
            ["فاطمة علي", "20", "4,200 دج"],
            ["محمد حسن", "18", "3,800 دج"],
            ["سارة أحمد", "15", "3,200 دج"],
            ["علي محمود", "12", "2,800 دج"]
        ]
        
        table.setRowCount(len(customers_data))
        for row, data in enumerate(customers_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, col, item)
        
        table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: none;
                gridline-color: #ecf0f1;
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setMaximumHeight(200)
        
        layout.addWidget(table)
        
        return frame
    
    def setup_auto_refresh(self):
        """إعداد التحديث التلقائي"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(60000)  # تحديث كل دقيقة
    
    def on_period_changed(self):
        """عند تغيير الفترة الزمنية"""
        self.load_analytics_data()
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_analytics_data()
        self.refresh_requested.emit()
    
    def load_analytics_data(self):
        """تحميل البيانات التحليلية"""
        try:
            # تحديث بطاقات المقاييس ببيانات عشوائية للعرض
            self.update_metric_cards()
            
            # تحديث الرسوم البيانية
            self.update_charts()
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات التحليلية: {e}")
    
    def update_metric_cards(self):
        """تحديث بطاقات المقاييس"""
        # بيانات تجريبية - سيتم استبدالها ببيانات حقيقية
        revenue = random.randint(10000, 50000)
        sales_count = random.randint(50, 200)
        new_customers = random.randint(5, 25)
        products_sold = random.randint(100, 500)
        
        # تحديث القيم
        self.revenue_card.findChild(QLabel).setText(f"{revenue:,.2f} دج")
        self.sales_card.findChild(QLabel).setText(str(sales_count))
        self.customers_card.findChild(QLabel).setText(str(new_customers))
        self.products_card.findChild(QLabel).setText(str(products_sold))
    
    def update_charts(self):
        """تحديث الرسوم البيانية"""
        # تحديث بيانات الرسوم البيانية ببيانات جديدة
        self.daily_sales_chart.update()
        self.top_products_chart.update()
        self.sales_trend_chart.update()
