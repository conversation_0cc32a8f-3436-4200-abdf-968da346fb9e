# دليل المستخدم - نظام نقطة البيع 📚

## مقدمة 🌟

مرحباً بك في نظام نقطة البيع المتقدم! هذا الدليل سيساعدك على استخدام جميع ميزات النظام بكفاءة.

## البدء السريع ⚡

### 1. تشغيل النظام
```bash
python main.py
```

### 2. تسجيل الدخول
- **المدير:** admin / admin123
- **الكاشير:** cashier / cashier123

### 3. شاشة البداية
ستظهر لك شاشة ترحيب لمدة 3 ثوان، ثم نافذة تسجيل الدخول.

## واجهة النظام الرئيسية 🖥️

### شريط المعلومات العلوي
- **معلومات المستخدم:** اسم المستخدم ودوره
- **التاريخ والوقت:** يتحدث تلقائياً كل ثانية
- **حالة الاتصال:** مؤشر الاتصال بقاعدة البيانات

### التبويبات الرئيسية
1. **🛒 المبيعات** - إجراء عمليات البيع
2. **📦 المخزون** - إدارة المنتجات والمخزون
3. **👥 العملاء** - إدارة بيانات العملاء
4. **📊 التقارير** - عرض التقارير والإحصائيات
5. **⚙️ الإعدادات** - تكوين النظام (للمدير فقط)

## تبويب المبيعات 🛒

### الجانب الأيسر - إدخال المنتجات

#### البحث عن منتج
1. **بالباركود:** امسح الباركود أو اكتبه في حقل "الباركود"
2. **اضغط Enter** أو زر "🔍 بحث"
3. ستظهر معلومات المنتج تلقائياً

#### معلومات المنتج
- **اسم المنتج:** يظهر تلقائياً بعد البحث
- **السعر:** سعر البيع للوحدة الواحدة
- **المخزون:** الكمية المتاحة (ملونة حسب الحالة)
  - 🟢 أخضر: مخزون كافي
  - 🟡 أصفر: مخزون منخفض
  - 🔴 أحمر: نفد المخزون

#### إضافة المنتج
1. **حدد الكمية** باستخدام مربع الأرقام
2. **اضغط "➕ إضافة"** أو F3
3. سيتم إضافة المنتج إلى الفاتورة

### الجانب الأيمن - الفاتورة الحالية

#### جدول المنتجات
- **المنتج:** اسم المنتج
- **السعر:** سعر الوحدة
- **الكمية:** عدد الوحدات
- **الخصم:** مبلغ الخصم (إن وجد)
- **المجموع:** إجمالي سعر هذا المنتج
- **حذف:** زر لحذف المنتج من الفاتورة

#### المجاميع
- **المجموع الفرعي:** مجموع أسعار جميع المنتجات
- **الخصم:** إجمالي الخصومات
- **الضريبة (19%):** ضريبة القيمة المضافة
- **المجموع النهائي:** المبلغ المطلوب دفعه

#### إتمام البيع
1. **💵 دفع نقدي:** للدفع النقدي
2. **💳 دفع بالبطاقة:** للدفع بالبطاقة الائتمانية

### عمليات سريعة
- **🆕 بيع جديد:** بدء فاتورة جديدة
- **⏸️ تعليق البيع:** حفظ الفاتورة للاحقاً
- **📋 استدعاء بيع:** استدعاء فاتورة معلقة

## نافذة الدفع النقدي 💵

### إدخال المبلغ
1. **المبلغ المطلوب:** يظهر تلقائياً
2. **المبلغ المدفوع:** أدخل المبلغ الذي دفعه العميل
3. **الباقي:** يحسب تلقائياً

### المبالغ السريعة
- أزرار للمبالغ الشائعة: 100، 200، 500، 1000، 2000، 5000، 10000 دج
- **المبلغ الدقيق:** لتعيين المبلغ المطلوب بالضبط

### إتمام الدفع
1. **تأكد من صحة المبلغ**
2. **أضف ملاحظات** إن أردت
3. **اضغط "✅ تأكيد الدفع"**

## تبويب المخزون 📦

### عرض المنتجات

#### البحث والفلترة
- **حقل البحث:** ابحث بالاسم أو الباركود
- **فلتر الفئة:** اختر فئة معينة
- **🔄 تحديث:** تحديث قائمة المنتجات

#### جدول المنتجات
- **الباركود:** رمز المنتج الفريد
- **اسم المنتج:** اسم المنتج
- **الفئة:** فئة المنتج
- **سعر الشراء:** التكلفة
- **سعر البيع:** سعر البيع
- **المخزون:** الكمية المتاحة (ملونة)
- **الحد الأدنى:** الحد الأدنى للتنبيه
- **الحالة:** نشط أو غير نشط

### إضافة منتج جديد

#### البيانات المطلوبة
- **الباركود:** (اختياري - سيتم توليده تلقائياً)
- **اسم المنتج:** (مطلوب)
- **الوصف:** وصف المنتج (اختياري)
- **الفئة:** اختر من القائمة
- **سعر الشراء:** تكلفة المنتج
- **سعر البيع:** (مطلوب)
- **الكمية الأولية:** المخزون الابتدائي
- **الحد الأدنى:** للتنبيه عند النفاد
- **الوحدة:** وحدة القياس (قطعة، كيلو، لتر...)

#### حفظ المنتج
1. **املأ البيانات المطلوبة**
2. **اضغط "💾 حفظ المنتج"**
3. **أو "🗑️ مسح النموذج"** للبدء من جديد

## تبويب العملاء 👥

### إضافة عميل جديد

#### بيانات العميل
- **الاسم:** (مطلوب)
- **الهاتف:** رقم الهاتف
- **البريد الإلكتروني:** عنوان البريد
- **العنوان:** عنوان العميل

#### حفظ العميل
1. **أدخل اسم العميل على الأقل**
2. **اضغط "💾 حفظ العميل"**

### قائمة العملاء
- **البحث:** ابحث بالاسم أو الهاتف
- **عرض البيانات:** جميع معلومات العملاء
- **نقاط الولاء:** نقاط العميل المتراكمة
- **إجمالي المشتريات:** مجموع مشتريات العميل

## تبويب التقارير 📊

### الإحصائيات السريعة
- **💰 إجمالي المبيعات:** مجموع المبيعات
- **🧾 عدد الفواتير:** عدد الفواتير المصدرة
- **📦 المنتجات المباعة:** عدد المنتجات
- **👥 العملاء الجدد:** العملاء الجدد

### التقارير المتاحة
1. **📊 تقرير المبيعات:** تفاصيل جميع المبيعات
2. **📦 تقرير المنتجات:** أداء المنتجات

### عرض التقارير
1. **اختر نوع التقرير**
2. **ستظهر البيانات في الجدول**
3. **يمكن تصدير التقارير لاحقاً**

## تبويب الإعدادات ⚙️ (للمدير فقط)

### معلومات المتجر
- **اسم المتجر:** اسم متجرك
- **العنوان:** عنوان المتجر
- **الهاتف:** رقم الهاتف
- **البريد الإلكتروني:** عنوان البريد
- **رقم التعريف الضريبي:** للفواتير الرسمية
- **السجل التجاري:** رقم السجل التجاري

### إعدادات النظام
- **العملة:** الدينار الجزائري (دج)
- **معدل الضريبة:** النسبة المئوية للضريبة
- **المنازل العشرية:** دقة الأسعار
- **الحد الأدنى الافتراضي:** للمنتجات الجديدة

## الاختصارات السريعة ⌨️

### اختصارات عامة
- **F1:** بيع جديد
- **F11:** ملء الشاشة
- **Ctrl+Q:** خروج من البرنامج

### اختصارات المبيعات
- **F2:** التركيز على حقل البحث
- **F3:** إضافة المنتج إلى السلة
- **F4:** دفع نقدي
- **F5:** دفع بالبطاقة
- **Delete:** مسح السلة

### اختصارات التنقل
- **Tab:** الانتقال للحقل التالي
- **Shift+Tab:** الانتقال للحقل السابق
- **Enter:** تأكيد أو بحث
- **Esc:** إلغاء أو إغلاق

## نصائح للاستخدام الأمثل 💡

### للكاشير
1. **احفظ الاختصارات** لتسريع العمل
2. **تأكد من المخزون** قبل البيع
3. **راجع الفاتورة** قبل إتمام البيع
4. **اطبع الفاتورة** دائماً للعميل

### للمدير
1. **راجع التقارير** يومياً
2. **تابع المخزون** المنخفض
3. **حدث أسعار المنتجات** حسب الحاجة
4. **اعمل نسخة احتياطية** دورياً

### للجميع
1. **سجل الخروج** عند الانتهاء
2. **لا تشارك كلمات المرور**
3. **أبلغ عن أي مشاكل** فوراً
4. **استخدم البحث** بدلاً من التصفح

## حل المشاكل الشائعة 🔧

### "لا يمكن العثور على المنتج"
- تأكد من صحة الباركود
- جرب البحث بالاسم
- تحقق من أن المنتج مضاف للنظام

### "مخزون غير كافي"
- تحقق من الكمية المتاحة
- قلل الكمية المطلوبة
- أو أضف مخزون جديد

### "خطأ في الطباعة"
- تأكد من تشغيل الطابعة
- تحقق من الاتصال
- راجع إعدادات الطباعة

### "بطء في النظام"
- أغلق البرامج الأخرى
- أعد تشغيل النظام
- تحقق من مساحة القرص

## الدعم والمساعدة 💬

### للحصول على المساعدة
1. **راجع هذا الدليل** أولاً
2. **جرب الحلول المقترحة**
3. **تواصل مع الدعم الفني**
4. **أرسل تفاصيل المشكلة**

### معلومات الاتصال
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +213-XXX-XXXXXX
- **ساعات العمل:** 8:00 - 17:00

---

**نتمنى لك تجربة ممتعة مع نظام نقطة البيع! 🌟**

*تم إعداد هذا الدليل بواسطة Augment Agent - 2025*
