/**
 * وحدة لوحة التحكم
 * Dashboard Module
 */

export class Dashboard {
    constructor(app) {
        this.app = app;
        this.storage = app.storage;
        this.utils = app.utils;
        this.notifications = app.notifications;
        
        this.charts = {};
        this.updateInterval = null;
    }

    /**
     * عرض لوحة التحكم
     */
    async render() {
        try {
            // تحديث الإحصائيات
            this.updateStats();
            
            // إنشاء الرسوم البيانية
            await this.createCharts();
            
            // تحديث القوائم
            this.updateRecentSales();
            this.updateLowStock();
            
            // بدء التحديث التلقائي
            this.startAutoUpdate();
            
        } catch (error) {
            console.error('خطأ في عرض لوحة التحكم:', error);
            this.notifications.error('حدث خطأ في تحميل لوحة التحكم');
        }
    }

    /**
     * تحديث الإحصائيات
     */
    updateStats() {
        try {
            const sales = this.storage.get('sales') || [];
            const customers = this.storage.get('customers') || [];
            const products = this.storage.get('products') || [];
            
            // تصفية مبيعات اليوم
            const today = new Date();
            const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const todaySales = sales.filter(sale => {
                const saleDate = new Date(sale.createdAt);
                return saleDate >= todayStart;
            });

            // حساب الإحصائيات
            const todayTotal = this.utils.sum(todaySales, 'total');
            const todayOrdersCount = todaySales.length;
            const totalCustomers = customers.filter(c => c.isActive).length;
            const totalProducts = products.filter(p => p.isActive).length;

            // تحديث العناصر في الواجهة
            this.updateStatElement('todaySales', this.utils.formatCurrency(todayTotal));
            this.updateStatElement('todayOrders', todayOrdersCount.toString());
            this.updateStatElement('totalCustomers', totalCustomers.toString());
            this.updateStatElement('totalProducts', totalProducts.toString());

        } catch (error) {
            console.error('خطأ في تحديث الإحصائيات:', error);
        }
    }

    /**
     * تحديث عنصر إحصائي
     */
    updateStatElement(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            // تأثير العد التصاعدي
            this.animateNumber(element, value);
        }
    }

    /**
     * تأثير العد التصاعدي للأرقام
     */
    animateNumber(element, targetValue) {
        const isNumber = !isNaN(parseFloat(targetValue));
        
        if (isNumber) {
            const target = parseFloat(targetValue);
            const current = parseFloat(element.textContent) || 0;
            const increment = (target - current) / 20;
            let currentValue = current;

            const timer = setInterval(() => {
                currentValue += increment;
                
                if ((increment > 0 && currentValue >= target) || 
                    (increment < 0 && currentValue <= target)) {
                    currentValue = target;
                    clearInterval(timer);
                }
                
                element.textContent = Math.round(currentValue).toString();
            }, 50);
        } else {
            element.textContent = targetValue;
        }
    }

    /**
     * إنشاء الرسوم البيانية
     */
    async createCharts() {
        try {
            // رسم بياني لمبيعات الأسبوع
            await this.createWeeklyChart();
            
            // رسم بياني لأفضل المنتجات
            await this.createProductsChart();
            
        } catch (error) {
            console.error('خطأ في إنشاء الرسوم البيانية:', error);
        }
    }

    /**
     * رسم بياني لمبيعات الأسبوع
     */
    async createWeeklyChart() {
        const canvas = document.getElementById('weeklyChart');
        if (!canvas) return;

        // تدمير الرسم البياني السابق إن وجد
        if (this.charts.weekly) {
            this.charts.weekly.destroy();
        }

        const sales = this.storage.get('sales') || [];
        const weekData = this.getWeeklyData(sales);

        const ctx = canvas.getContext('2d');
        this.charts.weekly = new Chart(ctx, {
            type: 'line',
            data: {
                labels: weekData.labels,
                datasets: [{
                    label: 'المبيعات (ريال سعودي)',
                    data: weekData.values,
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#3498db',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(163, 177, 198, 0.2)'
                        },
                        ticks: {
                            color: '#7f8c8d',
                            callback: function(value) {
                                return value.toLocaleString('ar-SA') + ' ر.س';
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(163, 177, 198, 0.2)'
                        },
                        ticks: {
                            color: '#7f8c8d'
                        }
                    }
                },
                elements: {
                    point: {
                        hoverBackgroundColor: '#2980b9'
                    }
                }
            }
        });
    }

    /**
     * رسم بياني لأفضل المنتجات
     */
    async createProductsChart() {
        const canvas = document.getElementById('productsChart');
        if (!canvas) return;

        // تدمير الرسم البياني السابق إن وجد
        if (this.charts.products) {
            this.charts.products.destroy();
        }

        const sales = this.storage.get('sales') || [];
        const productsData = this.getTopProductsData(sales);

        const ctx = canvas.getContext('2d');
        this.charts.products = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: productsData.labels,
                datasets: [{
                    data: productsData.values,
                    backgroundColor: [
                        '#3498db',
                        '#e74c3c',
                        '#2ecc71',
                        '#f39c12',
                        '#9b59b6'
                    ],
                    borderColor: '#ffffff',
                    borderWidth: 3,
                    hoverBorderWidth: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#7f8c8d',
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });
    }

    /**
     * الحصول على بيانات مبيعات الأسبوع
     */
    getWeeklyData(sales) {
        const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const weekData = new Array(7).fill(0);
        
        const today = new Date();
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay()); // بداية الأسبوع (الأحد)

        sales.forEach(sale => {
            const saleDate = new Date(sale.createdAt);
            const daysDiff = Math.floor((saleDate - weekStart) / (1000 * 60 * 60 * 24));
            
            if (daysDiff >= 0 && daysDiff < 7) {
                weekData[daysDiff] += sale.total || 0;
            }
        });

        return {
            labels: days,
            values: weekData
        };
    }

    /**
     * الحصول على بيانات أفضل المنتجات
     */
    getTopProductsData(sales) {
        const productSales = {};
        const products = this.storage.get('products') || [];

        // حساب مبيعات كل منتج
        sales.forEach(sale => {
            if (sale.items) {
                sale.items.forEach(item => {
                    if (!productSales[item.productId]) {
                        productSales[item.productId] = 0;
                    }
                    productSales[item.productId] += item.quantity || 0;
                });
            }
        });

        // ترتيب المنتجات حسب المبيعات
        const sortedProducts = Object.entries(productSales)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);

        const labels = [];
        const values = [];

        sortedProducts.forEach(([productId, quantity]) => {
            const product = products.find(p => p.id == productId);
            if (product) {
                labels.push(product.name);
                values.push(quantity);
            }
        });

        // إضافة بيانات افتراضية إذا لم توجد مبيعات
        if (labels.length === 0) {
            labels.push('لا توجد مبيعات');
            values.push(1);
        }

        return { labels, values };
    }

    /**
     * تحديث قائمة آخر المبيعات
     */
    updateRecentSales() {
        try {
            const sales = this.storage.get('sales') || [];
            const recentSales = sales
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                .slice(0, 5);

            const container = document.getElementById('recentSalesList');
            if (!container) return;

            if (recentSales.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-inbox" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
                        <p>لا توجد مبيعات حديثة</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = recentSales.map(sale => `
                <div class="recent-item">
                    <div class="item-info">
                        <div class="item-name">فاتورة #${sale.id}</div>
                        <div class="item-details">
                            ${this.utils.formatDate(sale.createdAt, 'relative')} - 
                            ${sale.customerName || 'عميل نقدي'}
                        </div>
                    </div>
                    <div class="item-value">
                        ${this.utils.formatCurrency(sale.total)}
                    </div>
                </div>
            `).join('');

        } catch (error) {
            console.error('خطأ في تحديث آخر المبيعات:', error);
        }
    }

    /**
     * تحديث قائمة المنتجات المنخفضة
     */
    updateLowStock() {
        try {
            const products = this.storage.get('products') || [];
            const lowStockProducts = products.filter(product => 
                product.isActive && 
                product.stock <= product.minStock
            );

            const container = document.getElementById('lowStockList');
            if (!container) return;

            if (lowStockProducts.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-check-circle" style="font-size: 2rem; margin-bottom: 0.5rem; color: #27ae60;"></i>
                        <p>جميع المنتجات متوفرة</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = lowStockProducts.map(product => `
                <div class="stock-item">
                    <div class="item-info">
                        <div class="item-name">${product.name}</div>
                        <div class="item-details">
                            الكمية المتبقية: ${product.stock} ${product.unit}
                        </div>
                    </div>
                    <div class="item-value">
                        <span class="badge neumorphic-badge badge-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            نفدت
                        </span>
                    </div>
                </div>
            `).join('');

        } catch (error) {
            console.error('خطأ في تحديث المنتجات المنخفضة:', error);
        }
    }

    /**
     * بدء التحديث التلقائي
     */
    startAutoUpdate() {
        // إيقاف التحديث السابق إن وجد
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }

        // تحديث كل 30 ثانية
        this.updateInterval = setInterval(() => {
            this.updateStats();
            this.updateRecentSales();
            this.updateLowStock();
        }, 30000);
    }

    /**
     * إيقاف التحديث التلقائي
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * تدمير الرسوم البيانية
     */
    destroyCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.destroy();
            }
        });
        this.charts = {};
    }

    /**
     * إعادة تحميل البيانات
     */
    reload() {
        this.render();
    }

    /**
     * تنظيف الموارد
     */
    destroy() {
        this.stopAutoUpdate();
        this.destroyCharts();
    }
}
