/**
 * وحدة المبيعات
 * Sales Module
 */

export class Sales {
    constructor(app) {
        this.app = app;
        this.storage = app.storage;
        this.utils = app.utils;
        this.notifications = app.notifications;
        
        this.currentSale = {
            id: null,
            items: [],
            subtotal: 0,
            tax: 0,
            discount: 0,
            total: 0,
            customer: null,
            paymentMethod: 'cash'
        };
        
        this.products = [];
        this.customers = [];
        this.settings = {};
    }

    /**
     * عرض واجهة المبيعات
     */
    async render() {
        try {
            // تحميل البيانات
            await this.loadData();
            
            // إنشاء واجهة المبيعات
            this.createSalesInterface();
            
            // ربط الأحداث
            this.bindEvents();
            
        } catch (error) {
            console.error('خطأ في عرض واجهة المبيعات:', error);
            this.notifications.error('حدث خطأ في تحميل واجهة المبيعات');
        }
    }

    /**
     * تحميل البيانات
     */
    async loadData() {
        this.products = this.storage.get('products') || [];
        this.customers = this.storage.get('customers') || [];
        this.settings = this.storage.get('settings') || {};
        
        // تصفية المنتجات النشطة فقط
        this.products = this.products.filter(p => p.isActive);
    }

    /**
     * إنشاء واجهة المبيعات
     */
    createSalesInterface() {
        const container = document.getElementById('sales-tab');
        if (!container) return;

        container.innerHTML = `
            <div class="sales-container">
                <!-- Header -->
                <div class="sales-header neumorphic-card">
                    <h2><i class="bi bi-cart3"></i> المبيعات</h2>
                    <div class="sales-actions">
                        <button class="btn neumorphic-btn" onclick="this.newSale()">
                            <i class="bi bi-plus-circle"></i>
                            فاتورة جديدة
                        </button>
                        <button class="btn neumorphic-btn" onclick="this.showSalesHistory()">
                            <i class="bi bi-clock-history"></i>
                            سجل المبيعات
                        </button>
                    </div>
                </div>

                <div class="sales-content">
                    <!-- Product Search -->
                    <div class="product-search-section neumorphic-card">
                        <div class="search-header">
                            <h4><i class="bi bi-search"></i> البحث عن المنتجات</h4>
                        </div>
                        <div class="search-controls">
                            <div class="search-input-group">
                                <input type="text" id="productSearch" class="form-control neumorphic-input" 
                                       placeholder="ابحث بالاسم أو الباركود...">
                                <button class="btn neumorphic-btn" onclick="this.scanBarcode()">
                                    <i class="bi bi-upc-scan"></i>
                                </button>
                            </div>
                            <select id="categoryFilter" class="form-select neumorphic-select">
                                <option value="">جميع الفئات</option>
                            </select>
                        </div>
                        <div id="productResults" class="product-results"></div>
                    </div>

                    <!-- Current Sale -->
                    <div class="current-sale-section neumorphic-card">
                        <div class="sale-header">
                            <h4><i class="bi bi-receipt"></i> الفاتورة الحالية</h4>
                            <div class="sale-info">
                                <span class="sale-number">فاتورة #<span id="saleNumber">جديدة</span></span>
                                <span class="sale-date" id="saleDate"></span>
                            </div>
                        </div>

                        <!-- Customer Selection -->
                        <div class="customer-section">
                            <label class="form-label">العميل:</label>
                            <div class="customer-controls">
                                <select id="customerSelect" class="form-select neumorphic-select">
                                    <option value="">عميل نقدي</option>
                                </select>
                                <button class="btn neumorphic-btn" onclick="this.addNewCustomer()">
                                    <i class="bi bi-person-plus"></i>
                                    عميل جديد
                                </button>
                            </div>
                        </div>

                        <!-- Sale Items -->
                        <div class="sale-items">
                            <div class="items-header">
                                <span>المنتج</span>
                                <span>السعر</span>
                                <span>الكمية</span>
                                <span>المجموع</span>
                                <span>إجراءات</span>
                            </div>
                            <div id="saleItemsList" class="items-list">
                                <div class="empty-sale">
                                    <i class="bi bi-cart-x"></i>
                                    <p>لا توجد منتجات في الفاتورة</p>
                                </div>
                            </div>
                        </div>

                        <!-- Sale Summary -->
                        <div class="sale-summary">
                            <div class="summary-row">
                                <span>المجموع الفرعي:</span>
                                <span id="subtotalAmount">0.00 ريال سعودي</span>
                            </div>
                            <div class="summary-row">
                                <span>الخصم:</span>
                                <div class="discount-controls">
                                    <input type="number" id="discountInput" class="form-control neumorphic-input" 
                                           placeholder="0" min="0" step="0.01">
                                    <select id="discountType" class="form-select neumorphic-select">
                                        <option value="amount">ريال</option>
                                        <option value="percent">%</option>
                                    </select>
                                </div>
                            </div>
                            <div class="summary-row">
                                <span>الضريبة (${this.getTaxRate()}%):</span>
                                <span id="taxAmount">0.00 ريال سعودي</span>
                            </div>
                            <div class="summary-row total-row">
                                <span>المجموع الإجمالي:</span>
                                <span id="totalAmount">0.00 ريال سعودي</span>
                            </div>
                        </div>

                        <!-- Payment Section -->
                        <div class="payment-section">
                            <div class="payment-method">
                                <label class="form-label">طريقة الدفع:</label>
                                <select id="paymentMethod" class="form-select neumorphic-select">
                                    <option value="cash">نقدي</option>
                                    <option value="card">بطاقة ائتمان</option>
                                    <option value="transfer">تحويل بنكي</option>
                                </select>
                            </div>
                            <div class="payment-actions">
                                <button class="btn neumorphic-btn btn-secondary" onclick="this.clearSale()">
                                    <i class="bi bi-x-circle"></i>
                                    مسح
                                </button>
                                <button class="btn neumorphic-btn btn-warning" onclick="this.holdSale()">
                                    <i class="bi bi-pause-circle"></i>
                                    تعليق
                                </button>
                                <button class="btn neumorphic-btn btn-success" onclick="this.completeSale()" id="completeSaleBtn">
                                    <i class="bi bi-check-circle"></i>
                                    إتمام البيع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // تحديث التاريخ
        document.getElementById('saleDate').textContent = this.utils.formatDate(new Date(), 'DD/MM/YYYY HH:mm');
        
        // تحميل العملاء
        this.loadCustomers();
        
        // تحميل الفئات
        this.loadCategories();
        
        // إنشاء رقم فاتورة جديد
        this.generateSaleNumber();
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // البحث عن المنتجات
        const productSearch = document.getElementById('productSearch');
        if (productSearch) {
            productSearch.addEventListener('input', this.searchProducts.bind(this));
            productSearch.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.searchProducts();
                }
            });
        }

        // تصفية الفئات
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', this.searchProducts.bind(this));
        }

        // حساب الخصم
        const discountInput = document.getElementById('discountInput');
        const discountType = document.getElementById('discountType');
        if (discountInput && discountType) {
            discountInput.addEventListener('input', this.calculateTotal.bind(this));
            discountType.addEventListener('change', this.calculateTotal.bind(this));
        }

        // اختيار العميل
        const customerSelect = document.getElementById('customerSelect');
        if (customerSelect) {
            customerSelect.addEventListener('change', this.selectCustomer.bind(this));
        }

        // طريقة الدفع
        const paymentMethod = document.getElementById('paymentMethod');
        if (paymentMethod) {
            paymentMethod.addEventListener('change', this.selectPaymentMethod.bind(this));
        }

        // ربط الدوال بالنافذة للوصول إليها من HTML
        window.salesModule = this;
    }

    /**
     * البحث عن المنتجات
     */
    searchProducts() {
        const searchTerm = document.getElementById('productSearch').value.trim();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const resultsContainer = document.getElementById('productResults');

        if (!resultsContainer) return;

        let filteredProducts = this.products;

        // تصفية حسب النص
        if (searchTerm) {
            filteredProducts = filteredProducts.filter(product => 
                this.utils.arabicSearch(product.name, searchTerm) ||
                product.barcode.includes(searchTerm)
            );
        }

        // تصفية حسب الفئة
        if (categoryFilter) {
            filteredProducts = filteredProducts.filter(product => 
                product.category === categoryFilter
            );
        }

        // عرض النتائج
        if (filteredProducts.length === 0) {
            resultsContainer.innerHTML = `
                <div class="no-results">
                    <i class="bi bi-search"></i>
                    <p>لا توجد منتجات مطابقة للبحث</p>
                </div>
            `;
            return;
        }

        resultsContainer.innerHTML = filteredProducts.map(product => `
            <div class="product-result-item neumorphic-card" onclick="salesModule.addProductToSale(${product.id})">
                <div class="product-info">
                    <div class="product-name">${product.name}</div>
                    <div class="product-details">
                        <span class="product-price">${this.utils.formatCurrency(product.sellingPrice)}</span>
                        <span class="product-stock ${product.stock <= product.minStock ? 'low-stock' : ''}">
                            المخزون: ${product.stock} ${product.unit}
                        </span>
                    </div>
                </div>
                <div class="product-actions">
                    <button class="btn neumorphic-btn btn-sm" onclick="event.stopPropagation(); salesModule.addProductToSale(${product.id})">
                        <i class="bi bi-plus"></i>
                        إضافة
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * إضافة منتج للفاتورة
     */
    addProductToSale(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) {
            this.notifications.error('المنتج غير موجود');
            return;
        }

        if (product.stock <= 0) {
            this.notifications.warning('المنتج غير متوفر في المخزون');
            return;
        }

        // فحص إذا كان المنتج موجود في الفاتورة
        const existingItem = this.currentSale.items.find(item => item.productId === productId);
        
        if (existingItem) {
            // زيادة الكمية
            if (existingItem.quantity < product.stock) {
                existingItem.quantity++;
                existingItem.total = existingItem.quantity * existingItem.price;
            } else {
                this.notifications.warning('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
                return;
            }
        } else {
            // إضافة منتج جديد
            this.currentSale.items.push({
                productId: product.id,
                name: product.name,
                price: product.sellingPrice,
                quantity: 1,
                total: product.sellingPrice,
                unit: product.unit
            });
        }

        // تحديث الواجهة
        this.updateSaleItems();
        this.calculateTotal();
        
        // تشغيل صوت
        this.utils.playSound('success');
        
        this.notifications.success(`تم إضافة ${product.name} للفاتورة`);
    }

    /**
     * تحديث قائمة منتجات الفاتورة
     */
    updateSaleItems() {
        const container = document.getElementById('saleItemsList');
        if (!container) return;

        if (this.currentSale.items.length === 0) {
            container.innerHTML = `
                <div class="empty-sale">
                    <i class="bi bi-cart-x"></i>
                    <p>لا توجد منتجات في الفاتورة</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.currentSale.items.map((item, index) => `
            <div class="sale-item neumorphic-card">
                <div class="item-name">${item.name}</div>
                <div class="item-price">${this.utils.formatCurrency(item.price)}</div>
                <div class="item-quantity">
                    <button class="btn neumorphic-btn btn-sm" onclick="salesModule.updateItemQuantity(${index}, -1)">
                        <i class="bi bi-dash"></i>
                    </button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="btn neumorphic-btn btn-sm" onclick="salesModule.updateItemQuantity(${index}, 1)">
                        <i class="bi bi-plus"></i>
                    </button>
                </div>
                <div class="item-total">${this.utils.formatCurrency(item.total)}</div>
                <div class="item-actions">
                    <button class="btn neumorphic-btn btn-sm btn-danger" onclick="salesModule.removeItem(${index})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * تحديث كمية منتج
     */
    updateItemQuantity(itemIndex, change) {
        const item = this.currentSale.items[itemIndex];
        if (!item) return;

        const product = this.products.find(p => p.id === item.productId);
        if (!product) return;

        const newQuantity = item.quantity + change;

        if (newQuantity <= 0) {
            this.removeItem(itemIndex);
            return;
        }

        if (newQuantity > product.stock) {
            this.notifications.warning('الكمية المطلوبة أكبر من المتوفر في المخزون');
            return;
        }

        item.quantity = newQuantity;
        item.total = item.quantity * item.price;

        this.updateSaleItems();
        this.calculateTotal();
    }

    /**
     * حذف منتج من الفاتورة
     */
    removeItem(itemIndex) {
        if (itemIndex >= 0 && itemIndex < this.currentSale.items.length) {
            const item = this.currentSale.items[itemIndex];
            this.currentSale.items.splice(itemIndex, 1);
            
            this.updateSaleItems();
            this.calculateTotal();
            
            this.notifications.info(`تم حذف ${item.name} من الفاتورة`);
        }
    }

    /**
     * حساب المجموع الإجمالي
     */
    calculateTotal() {
        // حساب المجموع الفرعي
        this.currentSale.subtotal = this.utils.sum(this.currentSale.items, 'total');

        // حساب الخصم
        const discountInput = document.getElementById('discountInput');
        const discountType = document.getElementById('discountType');
        
        let discountValue = parseFloat(discountInput?.value || 0);
        let discountAmount = 0;

        if (discountValue > 0) {
            if (discountType?.value === 'percent') {
                discountAmount = (this.currentSale.subtotal * discountValue) / 100;
            } else {
                discountAmount = discountValue;
            }
        }

        this.currentSale.discount = Math.min(discountAmount, this.currentSale.subtotal);

        // حساب المبلغ بعد الخصم
        const afterDiscount = this.currentSale.subtotal - this.currentSale.discount;

        // حساب الضريبة
        this.currentSale.tax = this.utils.calculateTax(afterDiscount, this.getTaxRate() / 100);

        // حساب المجموع الإجمالي
        this.currentSale.total = afterDiscount + this.currentSale.tax;

        // تحديث الواجهة
        this.updateSummaryDisplay();
    }

    /**
     * تحديث عرض الملخص
     */
    updateSummaryDisplay() {
        const subtotalElement = document.getElementById('subtotalAmount');
        const taxElement = document.getElementById('taxAmount');
        const totalElement = document.getElementById('totalAmount');

        if (subtotalElement) {
            subtotalElement.textContent = this.utils.formatCurrency(this.currentSale.subtotal);
        }

        if (taxElement) {
            taxElement.textContent = this.utils.formatCurrency(this.currentSale.tax);
        }

        if (totalElement) {
            totalElement.textContent = this.utils.formatCurrency(this.currentSale.total);
        }

        // تفعيل/تعطيل زر إتمام البيع
        const completeBtn = document.getElementById('completeSaleBtn');
        if (completeBtn) {
            completeBtn.disabled = this.currentSale.items.length === 0;
        }
    }

    /**
     * إتمام البيع
     */
    async completeSale() {
        try {
            if (this.currentSale.items.length === 0) {
                this.notifications.warning('لا يمكن إتمام فاتورة فارغة');
                return;
            }

            // تأكيد البيع
            const confirmed = await this.notifications.confirm(
                `هل تريد إتمام البيع بمبلغ ${this.utils.formatCurrency(this.currentSale.total)}؟`,
                'تأكيد البيع'
            );

            if (!confirmed) return;

            // إنشاء الفاتورة
            const sale = {
                id: this.generateSaleId(),
                number: document.getElementById('saleNumber').textContent,
                items: [...this.currentSale.items],
                subtotal: this.currentSale.subtotal,
                discount: this.currentSale.discount,
                tax: this.currentSale.tax,
                total: this.currentSale.total,
                customerId: this.currentSale.customer?.id || null,
                customerName: this.currentSale.customer?.name || 'عميل نقدي',
                paymentMethod: this.currentSale.paymentMethod,
                cashierId: this.app.getCurrentUser().id,
                cashierName: this.app.getCurrentUser().fullName,
                createdAt: new Date().toISOString(),
                status: 'completed'
            };

            // حفظ الفاتورة
            const sales = this.storage.get('sales') || [];
            sales.unshift(sale);
            this.storage.set('sales', sales);

            // تحديث المخزون
            await this.updateInventory(sale.items);

            // طباعة الفاتورة
            if (this.settings.pos?.printReceipt) {
                this.printReceipt(sale);
            }

            // مسح الفاتورة الحالية
            this.clearSale();

            this.notifications.success('تم إتمام البيع بنجاح');
            this.utils.playSound('success');

        } catch (error) {
            console.error('خطأ في إتمام البيع:', error);
            this.notifications.error('حدث خطأ في إتمام البيع');
        }
    }

    /**
     * تحديث المخزون
     */
    async updateInventory(items) {
        const products = this.storage.get('products') || [];
        
        items.forEach(item => {
            const productIndex = products.findIndex(p => p.id === item.productId);
            if (productIndex !== -1) {
                products[productIndex].stock -= item.quantity;
            }
        });

        this.storage.set('products', products);
        this.products = products.filter(p => p.isActive);
    }

    /**
     * مسح الفاتورة
     */
    clearSale() {
        this.currentSale = {
            id: null,
            items: [],
            subtotal: 0,
            tax: 0,
            discount: 0,
            total: 0,
            customer: null,
            paymentMethod: 'cash'
        };

        // مسح الحقول
        const discountInput = document.getElementById('discountInput');
        const customerSelect = document.getElementById('customerSelect');
        const paymentMethod = document.getElementById('paymentMethod');

        if (discountInput) discountInput.value = '';
        if (customerSelect) customerSelect.value = '';
        if (paymentMethod) paymentMethod.value = 'cash';

        // تحديث الواجهة
        this.updateSaleItems();
        this.calculateTotal();
        this.generateSaleNumber();

        this.notifications.info('تم مسح الفاتورة');
    }

    /**
     * تحميل العملاء
     */
    loadCustomers() {
        const customerSelect = document.getElementById('customerSelect');
        if (!customerSelect) return;

        const activeCustomers = this.customers.filter(c => c.isActive);
        
        customerSelect.innerHTML = '<option value="">عميل نقدي</option>' +
            activeCustomers.map(customer => 
                `<option value="${customer.id}">${customer.name}</option>`
            ).join('');
    }

    /**
     * تحميل الفئات
     */
    loadCategories() {
        const categoryFilter = document.getElementById('categoryFilter');
        if (!categoryFilter) return;

        const categories = [...new Set(this.products.map(p => p.category))];
        
        categoryFilter.innerHTML = '<option value="">جميع الفئات</option>' +
            categories.map(category => 
                `<option value="${category}">${category}</option>`
            ).join('');
    }

    /**
     * اختيار العميل
     */
    selectCustomer() {
        const customerSelect = document.getElementById('customerSelect');
        if (!customerSelect) return;

        const customerId = customerSelect.value;
        if (customerId) {
            this.currentSale.customer = this.customers.find(c => c.id == customerId);
        } else {
            this.currentSale.customer = null;
        }
    }

    /**
     * اختيار طريقة الدفع
     */
    selectPaymentMethod() {
        const paymentMethod = document.getElementById('paymentMethod');
        if (paymentMethod) {
            this.currentSale.paymentMethod = paymentMethod.value;
        }
    }

    /**
     * توليد رقم فاتورة
     */
    generateSaleNumber() {
        const today = new Date();
        const dateStr = today.toISOString().split('T')[0].replace(/-/g, '');
        const timeStr = today.getTime().toString().slice(-4);
        const saleNumber = `${dateStr}${timeStr}`;
        
        const saleNumberElement = document.getElementById('saleNumber');
        if (saleNumberElement) {
            saleNumberElement.textContent = saleNumber;
        }
        
        return saleNumber;
    }

    /**
     * توليد معرف فاتورة
     */
    generateSaleId() {
        const sales = this.storage.get('sales') || [];
        const maxId = sales.reduce((max, sale) => Math.max(max, sale.id || 0), 0);
        return maxId + 1;
    }

    /**
     * الحصول على معدل الضريبة
     */
    getTaxRate() {
        return (this.settings.pos?.taxRate || 0.15) * 100;
    }

    /**
     * طباعة الفاتورة
     */
    printReceipt(sale) {
        // سيتم تنفيذها لاحقاً
        console.log('طباعة الفاتورة:', sale);
    }

    /**
     * إعادة تحميل البيانات
     */
    reload() {
        this.loadData();
        this.loadCustomers();
        this.loadCategories();
        this.searchProducts();
    }
}
