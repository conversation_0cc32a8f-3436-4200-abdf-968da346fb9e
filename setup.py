#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد نظام نقطة البيع للتثبيت
Setup script for POS System installation
"""

from setuptools import setup, find_packages
import os

# قراءة ملف README
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# قراءة متطلبات النظام
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="advanced-pos-system",
    version="1.0.0",
    author="Augment Agent",
    author_email="<EMAIL>",
    description="نظام نقطة بيع متقدم باللغة العربية مع تصميم نيومورفيك",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/augment-code/advanced-pos-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: End Users/Desktop",
        "Topic :: Office/Business :: Financial :: Point-Of-Sale",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
        "Natural Language :: Arabic",
        "Environment :: X11 Applications :: Qt",
    ],
    python_requires=">=3.9",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-qt>=4.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "mypy>=0.950",
        ],
        "docs": [
            "sphinx>=4.0.0",
            "sphinx-rtd-theme>=1.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "pos-system=main:main",
            "pos=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.json", "*.ini"],
        "resources": ["*"],
    },
    keywords=[
        "pos", "point-of-sale", "retail", "arabic", "rtl", 
        "neumorphic", "pyqt5", "sqlite", "inventory", "sales"
    ],
    project_urls={
        "Bug Reports": "https://github.com/augment-code/advanced-pos-system/issues",
        "Source": "https://github.com/augment-code/advanced-pos-system",
        "Documentation": "https://docs.pos-system.com",
        "Funding": "https://github.com/sponsors/augment-code",
    },
)
