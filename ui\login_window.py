#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول - Login Window
واجهة تسجيل الدخول للنظام
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFrame, QMessageBox,
                            QCheckBox, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon

class LoginWindow(QDialog):
    """نافذة تسجيل الدخول"""
    
    login_successful = pyqtSignal(dict)  # إشارة نجاح تسجيل الدخول
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - نظام نقطة البيع")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # إطار الشعار والعنوان
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        
        # شعار النظام
        logo_label = QLabel("🏪")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: #3498db;
                margin-bottom: 10px;
            }
        """)
        
        # عنوان النظام
        title_label = QLabel("نظام نقطة البيع")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        
        # وصف النظام
        subtitle_label = QLabel("نظام شامل لإدارة المبيعات والمخزون")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 12px;
                margin-bottom: 20px;
            }
        """)
        
        header_layout.addWidget(logo_label)
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setStyleSheet("""
            QFrame {
                background-color: #e0e5ec;
                border-radius: 20px;
                padding: 20px;
                box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            }
        """)
        
        login_layout = QVBoxLayout(login_frame)
        login_layout.setSpacing(15)
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setStyleSheet("""
            QLineEdit {
                background-color: #e0e5ec;
                border: none;
                border-radius: 10px;
                padding: 12px 15px;
                font-size: 14px;
                color: #2c3e50;
                box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
            }
            QLineEdit:focus {
                box-shadow: inset 6px 6px 10px #a3b1c6, inset -6px -6px 10px #ffffff;
            }
        """)
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(self.username_input.styleSheet())
        
        # خيار تذكر المستخدم
        self.remember_checkbox = QCheckBox("تذكر بياناتي")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: #2c3e50;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                background-color: #e0e5ec;
                box-shadow: inset 2px 2px 4px #a3b1c6, inset -2px -2px 4px #ffffff;
            }
            QCheckBox::indicator:checked {
                background-color: #3498db;
                box-shadow: inset 2px 2px 4px #2980b9, inset -2px -2px 4px #5dade2;
            }
        """)
        
        # أزرار العمل
        buttons_layout = QHBoxLayout()
        
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 12px;
                padding: 12px 25px;
                font-size: 14px;
                font-weight: bold;
                box-shadow: 4px 4px 8px #2980b9, -4px -4px 8px #5dade2;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
                box-shadow: inset 2px 2px 4px #1b4f72, inset -2px -2px 4px #2980b9;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 12px;
                padding: 12px 25px;
                font-size: 14px;
                font-weight: bold;
                box-shadow: 4px 4px 8px #c0392b, -4px -4px 8px #ec7063;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
                box-shadow: inset 2px 2px 4px #922b21, inset -2px -2px 4px #c0392b;
            }
        """)
        
        buttons_layout.addWidget(self.login_button)
        buttons_layout.addWidget(self.cancel_button)
        
        # إضافة العناصر إلى تخطيط تسجيل الدخول
        login_layout.addWidget(username_label)
        login_layout.addWidget(self.username_input)
        login_layout.addWidget(password_label)
        login_layout.addWidget(self.password_input)
        login_layout.addWidget(self.remember_checkbox)
        login_layout.addSpacing(10)
        login_layout.addLayout(buttons_layout)
        
        # معلومات المستخدمين الافتراضيين
        info_frame = QFrame()
        info_layout = QVBoxLayout(info_frame)
        
        info_label = QLabel("المستخدمين الافتراضيين:")
        info_label.setStyleSheet("color: #7f8c8d; font-size: 11px; font-weight: bold;")
        
        admin_info = QLabel("المدير: admin / admin123")
        admin_info.setStyleSheet("color: #7f8c8d; font-size: 10px;")
        
        cashier_info = QLabel("الكاشير: cashier / cashier123")
        cashier_info.setStyleSheet("color: #7f8c8d; font-size: 10px;")
        
        info_layout.addWidget(info_label)
        info_layout.addWidget(admin_info)
        info_layout.addWidget(cashier_info)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(header_frame)
        main_layout.addWidget(login_frame)
        main_layout.addWidget(info_frame)
        main_layout.addStretch()
        
        self.setLayout(main_layout)
        
        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.password_input.returnPressed.connect(self.handle_login)
        self.username_input.returnPressed.connect(self.password_input.setFocus)
    
    def handle_login(self):
        """معالجة عملية تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            self.show_error("يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # تعطيل الأزرار أثناء المعالجة
        self.login_button.setEnabled(False)
        self.login_button.setText("جاري التحقق...")
        
        # التحقق من بيانات المستخدم
        user_data = self.db_manager.verify_user_login(username, password)
        
        if user_data:
            self.login_successful.emit(user_data)
            self.accept()
        else:
            self.show_error("اسم المستخدم أو كلمة المرور غير صحيحة")
            self.reset_login_button()
    
    def show_error(self, message):
        """عرض رسالة خطأ"""
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.setWindowTitle("خطأ في تسجيل الدخول")
        msg_box.setText(message)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()
    
    def reset_login_button(self):
        """إعادة تعيين زر تسجيل الدخول"""
        self.login_button.setEnabled(True)
        self.login_button.setText("تسجيل الدخول")
        self.password_input.clear()
        self.password_input.setFocus()
