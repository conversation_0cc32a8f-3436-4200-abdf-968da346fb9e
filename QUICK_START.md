# 🚀 دليل البدء السريع - Quick Start Guide

## 📋 المتطلبات الأساسية

### نظام التشغيل
- Windows 10/11 ✅
- Linux (Ubuntu 18.04+) ✅  
- macOS 10.14+ ✅

### البرمجيات المطلوبة
- Python 3.8 أو أحدث
- pip (مدير حزم Python)

## ⚡ التثبيت السريع

### 1. تحميل النظام
```bash
git clone https://github.com/your-repo/advanced-pos-system.git
cd advanced-pos-system
```

### 2. تشغيل النظام مباشرة
```bash
python run.py
```

سيقوم الملف تلقائياً بـ:
- فحص إصدار Python
- تثبيت المتطلبات المفقودة
- إنشاء المجلدات اللازمة
- تشغيل النظام

## 🔑 بيانات الدخول الافتراضية

### المدير (صلاحيات كاملة)
- **المستخدم**: `admin`
- **كلمة المرور**: `admin123`

### الكاشير (صلاحيات محدودة)
- **المستخدم**: `cashier`  
- **كلمة المرور**: `cashier123`

## 🛒 البدء في البيع

### 1. تسجيل الدخول
- شغل النظام
- أدخل بيانات الدخول
- اختر "تسجيل دخول"

### 2. إضافة منتج للبيع
**الطريقة الأولى - البحث:**
- اكتب اسم المنتج في حقل البحث
- اختر المنتج من النتائج
- أدخل الكمية

**الطريقة الثانية - الباركود:**
- امسح الباركود بالقارئ
- سيتم إضافة المنتج تلقائياً

**الطريقة الثالثة - الكاشير السريع:**
- انتقل لتبويب "كاشير سريع"
- اضغط على المنتج من الأزرار السريعة

### 3. إتمام البيع
- تأكد من صحة المنتجات والكميات
- اختر طريقة الدفع (نقدي/بطاقة)
- أدخل المبلغ المدفوع
- اطبع الإيصال

## 📦 إدارة المخزون

### إضافة منتج جديد
1. انتقل لتبويب "المخزون"
2. اضغط "إضافة منتج جديد"
3. أدخل البيانات:
   - اسم المنتج
   - الباركود
   - سعر الشراء والبيع
   - الكمية الأولية
   - الفئة
4. احفظ المنتج

### تحديث المخزون
1. ابحث عن المنتج
2. اضغط "تعديل"
3. حدث البيانات المطلوبة
4. احفظ التغييرات

## 👥 إدارة العملاء

### إضافة عميل جديد
1. انتقل لتبويب "العملاء"
2. اضغط "إضافة عميل جديد"
3. أدخل معلومات العميل
4. احفظ البيانات

### البحث عن عميل
- استخدم حقل البحث
- ابحث بالاسم أو رقم الهاتف
- اختر العميل من النتائج

## 📊 عرض التقارير

### تقرير المبيعات اليومية
1. انتقل لتبويب "التقارير"
2. اختر "تقرير المبيعات"
3. حدد التاريخ (اليوم افتراضياً)
4. اضغط "إنشاء التقرير"

### تقرير المخزون
1. اختر "تقرير المخزون"
2. حدد الفئة (اختياري)
3. اضغط "إنشاء التقرير"

## 🎯 الميزات المتقدمة

### الشاشة اللمسية
- انتقل لتبويب "شاشة لمس"
- واجهة محسنة للأجهزة اللوحية
- أزرار كبيرة وسهلة الاستخدام

### العروض والخصومات
1. انتقل لتبويب "العروض" (للمدير فقط)
2. اضغط "عرض جديد"
3. حدد نوع العرض:
   - خصم بالنسبة المئوية
   - خصم بمبلغ ثابت
   - اشتر واحصل على مجاني
4. حدد فترة العرض
5. احفظ العرض

### إدارة الموردين
1. انتقل لتبويب "الموردين" (للمدير فقط)
2. أضف موردين جدد
3. أنشئ طلبات شراء
4. تابع حالة الطلبات

## ⚙️ الإعدادات الأساسية

### إعدادات الشركة
1. انتقل لتبويب "الإعدادات" (للمدير فقط)
2. حدث معلومات الشركة:
   - اسم الشركة
   - العنوان
   - أرقام الهواتف
   - الرقم الضريبي
3. احفظ التغييرات

### إعدادات الطباعة
1. في الإعدادات، اختر "الطباعة"
2. حدد نوع الطابعة
3. اختر حجم الورق
4. فعل/ألغ الطباعة التلقائية

## 🔧 حل المشاكل الشائعة

### النظام لا يعمل
```bash
# تأكد من إصدار Python
python --version

# أعد تثبيت المتطلبات
pip install -r requirements.txt

# شغل النظام
python run.py
```

### خطأ في قاعدة البيانات
- احذف ملف `database/pos_database.db`
- أعد تشغيل النظام
- ستُنشأ قاعدة بيانات جديدة

### مشاكل الطباعة
- تأكد من تشغيل الطابعة
- تحقق من إعدادات الطباعة
- جرب طباعة تجريبية

### الواجهة لا تظهر بالعربية
- تأكد من تثبيت خطوط عربية
- أعد تشغيل النظام
- تحقق من إعدادات اللغة

## 📞 الحصول على المساعدة

### الدعم الفني
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX

### الموارد المفيدة
- [دليل المستخدم الكامل](docs/user_manual.pdf)
- [الأسئلة الشائعة](docs/faq.md)
- [فيديوهات تعليمية](https://youtube.com/pos-tutorials)

## 🔄 التحديثات

### فحص التحديثات
```bash
git pull origin main
pip install -r requirements.txt --upgrade
```

### النسخ الاحتياطية
- النظام ينشئ نسخ احتياطية تلقائياً
- موقع النسخ: مجلد `backups`
- تكرار النسخ: يومياً (قابل للتخصيص)

---

🎉 **مبروك! أنت الآن جاهز لاستخدام نظام نقطة البيع المتقدم**

💡 **نصيحة**: ابدأ بإضافة بعض المنتجات التجريبية لتتعرف على النظام قبل الاستخدام الفعلي.
