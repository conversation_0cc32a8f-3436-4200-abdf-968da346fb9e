#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية - Main Window
الواجهة الرئيسية لنظام نقطة البيع
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QTabWidget, QMenuBar, QStatusBar, QLabel, 
                            QToolBar, QAction, QMessageBox, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QIcon, QFont, QPixmap

from .sales_tab import SalesTab
from .inventory_tab import InventoryTab
from .reports_tab import ReportsTab
from .settings_tab import SettingsTab
from .customers_tab import CustomersTab

class MainWindow(QMainWindow):
    """النافذة الرئيسية للنظام"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"نظام نقطة البيع - مرحباً {self.user_data['full_name']}")
        self.setGeometry(100, 100, 1400, 900)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # شريط المعلومات العلوي
        info_frame = self.create_info_frame()
        main_layout.addWidget(info_frame)
        
        # التبويبات الرئيسية
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #e0e5ec;
                border-radius: 10px;
            }
            QTabBar::tab {
                background-color: #e0e5ec;
                color: #2c3e50;
                padding: 12px 20px;
                margin: 2px;
                border-radius: 8px;
                font-weight: bold;
                min-width: 100px;
                box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
                box-shadow: inset 2px 2px 4px #2980b9, inset -2px -2px 4px #5dade2;
            }
            QTabBar::tab:hover {
                background-color: #d1d9e6;
            }
        """)
        
        # إضافة التبويبات
        self.setup_tabs()
        
        main_layout.addWidget(self.tab_widget)
        
    def create_info_frame(self):
        """إنشاء إطار المعلومات العلوي"""
        info_frame = QFrame()
        info_frame.setFixedHeight(80)
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        
        layout = QHBoxLayout(info_frame)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # معلومات المستخدم
        user_info = QLabel(f"👤 المستخدم: {self.user_data['full_name']} ({self.user_data['role']})")
        user_info.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        
        # الوقت والتاريخ
        self.datetime_label = QLabel()
        self.datetime_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        
        # تحديث الوقت كل ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)
        self.update_datetime()
        
        # حالة الاتصال
        connection_status = QLabel("🟢 متصل")
        connection_status.setStyleSheet("""
            QLabel {
                color: #2ecc71;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(user_info)
        layout.addStretch()
        layout.addWidget(self.datetime_label)
        layout.addStretch()
        layout.addWidget(connection_status)
        
        return info_frame
    
    def setup_tabs(self):
        """إعداد التبويبات"""
        # تبويب المبيعات
        self.sales_tab = SalesTab(self.db_manager, self.user_data)
        self.tab_widget.addTab(self.sales_tab, "🛒 المبيعات")
        
        # تبويب المخزون (إذا كان المستخدم لديه صلاحية)
        if self.user_data['role'] in ['admin', 'manager'] or \
           self.user_data['permissions'].get('inventory', False):
            self.inventory_tab = InventoryTab(self.db_manager, self.user_data)
            self.tab_widget.addTab(self.inventory_tab, "📦 المخزون")
        
        # تبويب العملاء
        self.customers_tab = CustomersTab(self.db_manager, self.user_data)
        self.tab_widget.addTab(self.customers_tab, "👥 العملاء")
        
        # تبويب التقارير (إذا كان المستخدم لديه صلاحية)
        if self.user_data['role'] in ['admin', 'manager'] or \
           self.user_data['permissions'].get('reports', False):
            self.reports_tab = ReportsTab(self.db_manager, self.user_data)
            self.tab_widget.addTab(self.reports_tab, "📊 التقارير")
        
        # تبويب الإعدادات (للمدير فقط)
        if self.user_data['role'] == 'admin':
            self.settings_tab = SettingsTab(self.db_manager, self.user_data)
            self.tab_widget.addTab(self.settings_tab, "⚙️ الإعدادات")
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: #34495e;
                color: white;
                border: none;
                padding: 5px;
                font-weight: bold;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                border-radius: 5px;
            }
            QMenuBar::item:selected {
                background-color: #3498db;
            }
        """)
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        # إجراءات قائمة الملف
        new_sale_action = QAction("بيع جديد", self)
        new_sale_action.setShortcut("Ctrl+N")
        new_sale_action.triggered.connect(self.new_sale)
        file_menu.addAction(new_sale_action)
        
        file_menu.addSeparator()
        
        backup_action = QAction("نسخ احتياطي", self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        logout_action = QAction("تسجيل خروج", self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu("عرض")
        
        fullscreen_action = QAction("ملء الشاشة", self)
        fullscreen_action.setShortcut("F11")
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = QToolBar()
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #e0e5ec;
                border: none;
                padding: 5px;
                spacing: 10px;
            }
            QToolButton {
                background-color: #e0e5ec;
                border: none;
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
                font-weight: bold;
                box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
            }
            QToolButton:hover {
                background-color: #d1d9e6;
            }
            QToolButton:pressed {
                background-color: #b8c6db;
                box-shadow: inset 2px 2px 4px #a3b1c6, inset -2px -2px 4px #ffffff;
            }
        """)
        
        # أزرار شريط الأدوات
        new_sale_action = QAction("🛒 بيع جديد", self)
        new_sale_action.triggered.connect(self.new_sale)
        toolbar.addAction(new_sale_action)
        
        toolbar.addSeparator()
        
        search_action = QAction("🔍 بحث", self)
        search_action.triggered.connect(self.show_search)
        toolbar.addAction(search_action)
        
        calculator_action = QAction("🧮 آلة حاسبة", self)
        calculator_action.triggered.connect(self.show_calculator)
        toolbar.addAction(calculator_action)
        
        self.addToolBar(toolbar)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #34495e;
                color: white;
                border: none;
                padding: 5px;
            }
        """)
        
        status_bar.showMessage("جاهز للعمل")
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # يمكن إضافة المزيد من الاتصالات هنا
        pass
    
    def update_datetime(self):
        """تحديث الوقت والتاريخ"""
        from datetime import datetime
        now = datetime.now()
        datetime_str = now.strftime("📅 %Y/%m/%d - 🕐 %H:%M:%S")
        self.datetime_label.setText(datetime_str)
    
    def new_sale(self):
        """بدء بيع جديد"""
        self.tab_widget.setCurrentIndex(0)  # الانتقال إلى تبويب المبيعات
        if hasattr(self.sales_tab, 'new_sale'):
            self.sales_tab.new_sale()
    
    def show_search(self):
        """عرض نافذة البحث"""
        # سيتم تنفيذها لاحقاً
        QMessageBox.information(self, "البحث", "ميزة البحث قيد التطوير")
    
    def show_calculator(self):
        """عرض الآلة الحاسبة"""
        # سيتم تنفيذها لاحقاً
        QMessageBox.information(self, "الآلة الحاسبة", "الآلة الحاسبة قيد التطوير")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        # سيتم تنفيذها لاحقاً
        QMessageBox.information(self, "النسخ الاحتياطي", "تم إنشاء النسخة الاحتياطية بنجاح")
    
    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
        <h2>نظام نقطة البيع المتقدم</h2>
        <p><b>الإصدار:</b> 1.0.0</p>
        <p><b>المطور:</b> Augment Agent</p>
        <p><b>التاريخ:</b> 2025-06-15</p>
        <br>
        <p>نظام شامل لإدارة المبيعات والمخزون مع واجهة عربية حديثة</p>
        <p>يدعم الكتابة من اليمين إلى اليسار وتصميم نيومورفيك</p>
        """
        
        QMessageBox.about(self, "حول البرنامج", about_text)
    
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(
            self, "تسجيل الخروج", 
            "هل أنت متأكد من تسجيل الخروج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.close()
            # إعادة عرض نافذة تسجيل الدخول
            from .login_window import LoginWindow
            login_window = LoginWindow(self.db_manager)
            login_window.exec_()
    
    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        reply = QMessageBox.question(
            self, "إغلاق البرنامج", 
            "هل أنت متأكد من إغلاق البرنامج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # حفظ أي بيانات مؤقتة
            event.accept()
        else:
            event.ignore()
