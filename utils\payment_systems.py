#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أنظمة الدفع الإلكتروني - Electronic Payment Systems
دعم أنظمة الدفع المختلفة والمحافظ الإلكترونية
"""

import json
import requests
from typing import Dict, Any, Optional, List
from PyQt5.QtCore import QObject, pyqtSignal, QThread
from PyQt5.QtWidgets import QMessageBox
from datetime import datetime
import hashlib
import uuid

class PaymentResult:
    """نتيجة عملية الدفع"""
    
    def __init__(self, success: bool, transaction_id: str = "", 
                 message: str = "", amount: float = 0.0, 
                 payment_method: str = "", reference: str = ""):
        self.success = success
        self.transaction_id = transaction_id
        self.message = message
        self.amount = amount
        self.payment_method = payment_method
        self.reference = reference
        self.timestamp = datetime.now()

class BasePaymentProvider(QObject):
    """مزود دفع أساسي"""
    
    payment_completed = pyqtSignal(PaymentResult)
    payment_failed = pyqtSignal(str)
    payment_progress = pyqtSignal(str)
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.enabled = config.get('enabled', False)
        self.test_mode = config.get('test_mode', True)
        
    def process_payment(self, amount: float, currency: str = "DZD", 
                       customer_info: Dict = None) -> PaymentResult:
        """معالجة الدفع - يجب تنفيذها في الفئات المشتقة"""
        raise NotImplementedError
    
    def refund_payment(self, transaction_id: str, amount: float = None) -> PaymentResult:
        """استرداد الدفع"""
        raise NotImplementedError
    
    def verify_payment(self, transaction_id: str) -> PaymentResult:
        """التحقق من حالة الدفع"""
        raise NotImplementedError

class CashPaymentProvider(BasePaymentProvider):
    """مزود الدفع النقدي"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
    def process_payment(self, amount: float, currency: str = "DZD", 
                       customer_info: Dict = None) -> PaymentResult:
        """معالجة الدفع النقدي"""
        transaction_id = f"CASH_{uuid.uuid4().hex[:8].upper()}"
        
        return PaymentResult(
            success=True,
            transaction_id=transaction_id,
            message="تم الدفع النقدي بنجاح",
            amount=amount,
            payment_method="نقدي",
            reference=transaction_id
        )

class CardPaymentProvider(BasePaymentProvider):
    """مزود الدفع بالبطاقة"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.terminal_id = config.get('terminal_id', 'TERM001')
        self.merchant_id = config.get('merchant_id', 'MERCH001')
        
    def process_payment(self, amount: float, currency: str = "DZD", 
                       customer_info: Dict = None) -> PaymentResult:
        """معالجة الدفع بالبطاقة"""
        self.payment_progress.emit("جاري الاتصال بالبنك...")
        
        # محاكاة عملية الدفع
        transaction_id = f"CARD_{uuid.uuid4().hex[:8].upper()}"
        
        if self.test_mode:
            # في وضع الاختبار، نجح الدفع دائماً
            return PaymentResult(
                success=True,
                transaction_id=transaction_id,
                message="تم الدفع بالبطاقة بنجاح",
                amount=amount,
                payment_method="بطاقة ائتمان",
                reference=transaction_id
            )
        else:
            # هنا سيتم الاتصال الفعلي بنظام البنك
            return self._process_real_card_payment(amount, currency, customer_info)
    
    def _process_real_card_payment(self, amount: float, currency: str, 
                                  customer_info: Dict) -> PaymentResult:
        """معالجة الدفع الفعلي بالبطاقة"""
        # سيتم تنفيذها مع API البنك الفعلي
        return PaymentResult(
            success=False,
            message="نظام الدفع بالبطاقة غير متاح حالياً"
        )

class BaridiMobProvider(BasePaymentProvider):
    """مزود بريدي موب"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get('api_key', '')
        self.merchant_code = config.get('merchant_code', '')
        self.base_url = config.get('base_url', 'https://api.baridimob.dz')
        
    def process_payment(self, amount: float, currency: str = "DZD", 
                       customer_info: Dict = None) -> PaymentResult:
        """معالجة الدفع عبر بريدي موب"""
        if not self.enabled:
            return PaymentResult(
                success=False,
                message="بريدي موب غير مفعل"
            )
        
        self.payment_progress.emit("جاري الاتصال ببريدي موب...")
        
        transaction_id = f"BM_{uuid.uuid4().hex[:8].upper()}"
        
        if self.test_mode:
            return PaymentResult(
                success=True,
                transaction_id=transaction_id,
                message="تم الدفع عبر بريدي موب بنجاح",
                amount=amount,
                payment_method="بريدي موب",
                reference=transaction_id
            )
        
        # الاتصال الفعلي بـ API بريدي موب
        return self._process_baridimob_payment(amount, currency, customer_info)
    
    def _process_baridimob_payment(self, amount: float, currency: str, 
                                  customer_info: Dict) -> PaymentResult:
        """معالجة الدفع الفعلي عبر بريدي موب"""
        try:
            payload = {
                'amount': amount,
                'currency': currency,
                'merchant_code': self.merchant_code,
                'transaction_id': f"BM_{uuid.uuid4().hex[:8].upper()}",
                'customer_phone': customer_info.get('phone', '') if customer_info else ''
            }
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                f"{self.base_url}/payment/initiate",
                json=payload,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return PaymentResult(
                    success=result.get('success', False),
                    transaction_id=result.get('transaction_id', ''),
                    message=result.get('message', ''),
                    amount=amount,
                    payment_method="بريدي موب"
                )
            else:
                return PaymentResult(
                    success=False,
                    message=f"خطأ في الاتصال ببريدي موب: {response.status_code}"
                )
                
        except Exception as e:
            return PaymentResult(
                success=False,
                message=f"خطأ في الدفع عبر بريدي موب: {str(e)}"
            )

class CIBPaymentProvider(BasePaymentProvider):
    """مزود الدفع عبر CIB"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.merchant_id = config.get('merchant_id', '')
        self.secret_key = config.get('secret_key', '')
        self.base_url = config.get('base_url', 'https://payment.cib.dz')
        
    def process_payment(self, amount: float, currency: str = "DZD", 
                       customer_info: Dict = None) -> PaymentResult:
        """معالجة الدفع عبر CIB"""
        if not self.enabled:
            return PaymentResult(
                success=False,
                message="نظام CIB غير مفعل"
            )
        
        self.payment_progress.emit("جاري الاتصال بـ CIB...")
        
        transaction_id = f"CIB_{uuid.uuid4().hex[:8].upper()}"
        
        if self.test_mode:
            return PaymentResult(
                success=True,
                transaction_id=transaction_id,
                message="تم الدفع عبر CIB بنجاح",
                amount=amount,
                payment_method="CIB",
                reference=transaction_id
            )
        
        return self._process_cib_payment(amount, currency, customer_info)
    
    def _process_cib_payment(self, amount: float, currency: str, 
                            customer_info: Dict) -> PaymentResult:
        """معالجة الدفع الفعلي عبر CIB"""
        # سيتم تنفيذها مع API CIB الفعلي
        return PaymentResult(
            success=False,
            message="نظام CIB قيد التطوير"
        )

class PaymentManager(QObject):
    """مدير أنظمة الدفع"""
    
    payment_completed = pyqtSignal(PaymentResult)
    payment_failed = pyqtSignal(str)
    payment_progress = pyqtSignal(str)
    
    def __init__(self, config_file: str = "payment_config.json"):
        super().__init__()
        self.config_file = config_file
        self.providers = {}
        self.load_config()
        self.setup_providers()
        
    def load_config(self):
        """تحميل إعدادات الدفع"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            # إنشاء ملف إعدادات افتراضي
            self.config = self.create_default_config()
            self.save_config()
        except Exception as e:
            print(f"خطأ في تحميل إعدادات الدفع: {e}")
            self.config = self.create_default_config()
    
    def create_default_config(self):
        """إنشاء إعدادات افتراضية"""
        return {
            "cash": {
                "enabled": True,
                "name": "الدفع النقدي",
                "icon": "💵"
            },
            "card": {
                "enabled": True,
                "name": "بطاقة ائتمان",
                "icon": "💳",
                "test_mode": True,
                "terminal_id": "TERM001",
                "merchant_id": "MERCH001"
            },
            "baridimob": {
                "enabled": False,
                "name": "بريدي موب",
                "icon": "📱",
                "test_mode": True,
                "api_key": "",
                "merchant_code": "",
                "base_url": "https://api.baridimob.dz"
            },
            "cib": {
                "enabled": False,
                "name": "CIB",
                "icon": "🏦",
                "test_mode": True,
                "merchant_id": "",
                "secret_key": "",
                "base_url": "https://payment.cib.dz"
            }
        }
    
    def save_config(self):
        """حفظ الإعدادات"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الدفع: {e}")
    
    def setup_providers(self):
        """إعداد مزودي الدفع"""
        # الدفع النقدي
        if self.config.get('cash', {}).get('enabled', True):
            self.providers['cash'] = CashPaymentProvider(self.config['cash'])
        
        # الدفع بالبطاقة
        if self.config.get('card', {}).get('enabled', True):
            self.providers['card'] = CardPaymentProvider(self.config['card'])
        
        # بريدي موب
        if self.config.get('baridimob', {}).get('enabled', False):
            self.providers['baridimob'] = BaridiMobProvider(self.config['baridimob'])
        
        # CIB
        if self.config.get('cib', {}).get('enabled', False):
            self.providers['cib'] = CIBPaymentProvider(self.config['cib'])
        
        # ربط الإشارات
        for provider in self.providers.values():
            provider.payment_completed.connect(self.payment_completed.emit)
            provider.payment_failed.connect(self.payment_failed.emit)
            provider.payment_progress.connect(self.payment_progress.emit)
    
    def get_available_methods(self) -> List[Dict[str, str]]:
        """الحصول على طرق الدفع المتاحة"""
        methods = []
        
        for key, provider in self.providers.items():
            if provider.enabled:
                config = self.config.get(key, {})
                methods.append({
                    'id': key,
                    'name': config.get('name', key),
                    'icon': config.get('icon', '💳')
                })
        
        return methods
    
    def process_payment(self, method: str, amount: float, 
                       currency: str = "DZD", customer_info: Dict = None) -> PaymentResult:
        """معالجة الدفع"""
        if method not in self.providers:
            return PaymentResult(
                success=False,
                message=f"طريقة الدفع {method} غير متاحة"
            )
        
        provider = self.providers[method]
        
        try:
            result = provider.process_payment(amount, currency, customer_info)
            
            # تسجيل العملية
            self.log_transaction(result)
            
            return result
            
        except Exception as e:
            error_msg = f"خطأ في معالجة الدفع: {str(e)}"
            self.payment_failed.emit(error_msg)
            return PaymentResult(success=False, message=error_msg)
    
    def log_transaction(self, result: PaymentResult):
        """تسجيل العملية"""
        log_entry = {
            'timestamp': result.timestamp.isoformat(),
            'transaction_id': result.transaction_id,
            'amount': result.amount,
            'payment_method': result.payment_method,
            'success': result.success,
            'message': result.message
        }
        
        # هنا يمكن حفظ السجل في قاعدة البيانات أو ملف
        print(f"تم تسجيل العملية: {log_entry}")
    
    def refund_payment(self, method: str, transaction_id: str, 
                      amount: float = None) -> PaymentResult:
        """استرداد الدفع"""
        if method not in self.providers:
            return PaymentResult(
                success=False,
                message=f"طريقة الدفع {method} غير متاحة"
            )
        
        provider = self.providers[method]
        return provider.refund_payment(transaction_id, amount)
    
    def verify_payment(self, method: str, transaction_id: str) -> PaymentResult:
        """التحقق من حالة الدفع"""
        if method not in self.providers:
            return PaymentResult(
                success=False,
                message=f"طريقة الدفع {method} غير متاحة"
            )
        
        provider = self.providers[method]
        return provider.verify_payment(transaction_id)
